[{"timestamp": "2025-07-07T16:17:58.460875", "total_files": 118, "total_links": 2423, "broken_links": 491, "success_rate": 79.7358646306232, "critical_files_status": {"000-chinook-index.md": 16, "050-chinook-advanced-features-guide.md": 1, "060-chinook-media-library-guide.md": 0, "070-chinook-hierarchy-comparison-guide.md": 1, "filament/setup/000-index.md": 2, "filament/resources/000-index.md": 2, "packages/000-packages-index.md": 17, "testing/000-testing-index.md": 0}, "new_issues": [], "resolved_issues": [], "status": "FAIL", "execution_time": 0.490678071975708}, {"timestamp": "2025-07-07T17:03:23.430397", "total_files": 124, "total_links": 2528, "broken_links": 471, "success_rate": 81.36867088607595, "critical_files_status": {"000-chinook-index.md": 19, "050-chinook-advanced-features-guide.md": 1, "060-chinook-media-library-guide.md": 0, "070-chinook-hierarchy-comparison-guide.md": 1, "filament/setup/000-index.md": 2, "filament/resources/000-index.md": 2, "packages/000-packages-index.md": 19, "testing/000-testing-index.md": 0}, "new_issues": [{"file": "040-chinook-seeders-guide.md", "link": "#customer-seeder", "text": "Customer Seeder", "status": "Anchor not found: #customer-seeder"}, {"file": "040-chinook-seeders-guide.md", "link": "#playlist-seeder", "text": "Playlist Seeder", "status": "Anchor not found: #playlist-seeder"}, {"file": "040-chinook-seeders-guide.md", "link": "#track-seeder", "text": "Track Seeder", "status": "Anchor not found: #track-seeder"}, {"file": "040-chinook-seeders-guide.md", "link": "#invoice-seeder", "text": "Invoice Seeder", "status": "Anchor not found: #invoice-seeder"}, {"file": "040-chinook-seeders-guide.md", "link": "#invoiceline-seeder", "text": "InvoiceLine Seeder", "status": "Anchor not found: #invoiceline-seeder"}, {"file": "040-chinook-seeders-guide.md", "link": "#playlisttrack-seeder", "text": "Playlist<PERSON><PERSON>", "status": "Anchor not found: #playlisttrack-seeder"}, {"file": "040-chinook-seeders-guide.md", "link": "#database-seeder", "text": "Database Seeder", "status": "Anchor not found: #database-seeder"}, {"file": "README.md", "link": "../../reports/chinook/DOCUMENTATION_AUDIT_REPORT.md", "text": "Documentation Audit Report", "status": "Path outside base directory: .ai/guides/chinook/../../reports/chinook/DOCUMENTATION_AUDIT_REPORT.md"}, {"file": "000-chinook-index.md", "link": "#8-panel-setup--configuration", "text": "8. Panel Setup & Configuration", "status": "Anchor not found: #8-panel-setup--configuration"}, {"file": "000-chinook-index.md", "link": "#9-model-standards--architecture", "text": "9. Model Standards & Architecture", "status": "Anchor not found: #9-model-standards--architecture"}, {"file": "000-chinook-index.md", "link": "#11-advanced-features--widgets", "text": "11. Advanced Features & Widgets", "status": "Anchor not found: #11-advanced-features--widgets"}, {"file": "000-chinook-index.md", "link": "#12-testing--quality-assurance", "text": "12. Testing & Quality Assurance", "status": "Anchor not found: #12-testing--quality-assurance"}, {"file": "000-chinook-index.md", "link": "#13-deployment--production", "text": "13. Deployment & Production", "status": "Anchor not found: #13-deployment--production"}, {"file": "000-chinook-index.md", "link": "#14-visual-documentation--diagrams", "text": "14. Visual Documentation & Diagrams", "status": "Anchor not found: #14-visual-documentation--diagrams"}, {"file": "000-chinook-index.md", "link": "#15-frontend-architecture--patterns", "text": "15. Frontend Architecture & Patterns", "status": "Anchor not found: #15-frontend-architecture--patterns"}, {"file": "000-chinook-index.md", "link": "#16-livewire-volt-integration", "text": "16. Livewire/Volt Integration", "status": "Anchor not found: #16-livewire-volt-integration"}, {"file": "000-chinook-index.md", "link": "#17-performance--accessibility", "text": "17. Performance & Accessibility", "status": "Anchor not found: #17-performance--accessibility"}, {"file": "000-chinook-index.md", "link": "#18-testing--cicd", "text": "18. Testing & CI/CD", "status": "Anchor not found: #18-testing--cicd"}, {"file": "000-chinook-index.md", "link": "#testing--quality-assurance", "text": "Testing & Quality Assurance", "status": "Anchor not found: #testing--quality-assurance"}, {"file": "000-chinook-index.md", "link": "#database--data", "text": "Database & Data", "status": "Anchor not found: #database--data"}, {"file": "000-chinook-index.md", "link": "../../reports/chinook/DOCUMENTATION_AUDIT_REPORT.md", "text": "Documentation Audit Report", "status": "Path outside base directory: .ai/guides/chinook/../../reports/chinook/DOCUMENTATION_AUDIT_REPORT.md"}, {"file": "000-chinook-index.md", "link": "filament/010-panel-setup-guide.md", "text": "Filament Panel Setup", "status": "File not found: filament/010-panel-setup-guide.md"}, {"file": "000-chinook-index.md", "link": "filament/models/010-model-standards-guide.md", "text": "Model Standards", "status": "File not found: filament/models/010-model-standards-guide.md"}, {"file": "000-chinook-index.md", "link": "filament/040-advanced-features-guide.md", "text": "Advanced Features", "status": "File not found: filament/040-advanced-features-guide.md"}, {"file": "000-chinook-index.md", "link": "filament/deployment/010-deployment-guide.md", "text": "Deployment Guide", "status": "File not found: filament/deployment/010-deployment-guide.md"}, {"file": "000-chinook-index.md", "link": "014-visual-documentation-guide.md", "text": "Visual Documentation", "status": "File not found: 014-visual-documentation-guide.md"}, {"file": "000-chinook-index.md", "link": "frontend/160-livewire-volt-integration-guide.md", "text": "Livewire Integration", "status": "File not found: frontend/160-livewire-volt-integration-guide.md"}, {"file": "050-chinook-advanced-features-guide.md", "link": "#rbac-authentication--authorization-flow", "text": "RBAC Authentication & Authorization Flow", "status": "Anchor not found: #rbac-authentication--authorization-flow"}, {"file": "070-chinook-hierarchy-comparison-guide.md", "link": "../../testing/000-testing-index.md", "text": "Testing Guide", "status": "Path outside base directory: .ai/guides/chinook/../../testing/000-testing-index.md"}, {"file": "frontend/180-api-testing-guide.md", "link": "#search-and-discovery-testing", "text": "Search and Discovery Testing", "status": "Anchor not found: #search-and-discovery-testing"}, {"file": "frontend/180-api-testing-guide.md", "link": "#analytics-api-testing", "text": "Analytics API Testing", "status": "Anchor not found: #analytics-api-testing"}, {"file": "frontend/180-api-testing-guide.md", "link": "#test-automation", "text": "Test Automation", "status": "Anchor not found: #test-automation"}, {"file": "frontend/190-cicd-integration-guide.md", "link": "#monitoring-and-notifications", "text": "Monitoring and Notifications", "status": "Anchor not found: #monitoring-and-notifications"}, {"file": "frontend/190-cicd-integration-guide.md", "link": "200-media-library-enhancement-guide.md", "text": "Media Library Enhancement Guide", "status": "File not found: frontend/200-media-library-enhancement-guide.md"}, {"file": "frontend/000-frontend-index.md", "link": "#frontend-architecture-overview", "text": "Frontend Architecture Overview", "status": "Anchor not found: #frontend-architecture-overview"}, {"file": "frontend/000-frontend-index.md", "link": "#volt-functional-component-patterns", "text": "Volt Functional Component Patterns", "status": "Anchor not found: #volt-functional-component-patterns"}, {"file": "frontend/000-frontend-index.md", "link": "#fluxflux-pro-component-integration", "text": "Flux/Flux-Pro Component Integration", "status": "Anchor not found: #fluxflux-pro-component-integration"}, {"file": "frontend/000-frontend-index.md", "link": "#spa-navigation-implementation", "text": "SPA Navigation Implementation", "status": "Anchor not found: #spa-navigation-implementation"}, {"file": "frontend/000-frontend-index.md", "link": "#accessibility-and-wcag-compliance", "text": "Accessibility and WCAG Compliance", "status": "Anchor not found: #accessibility-and-wcag-compliance"}, {"file": "frontend/000-frontend-index.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "frontend/000-frontend-index.md", "link": "#testing-strategies", "text": "Testing Strategies", "status": "Anchor not found: #testing-strategies"}, {"file": "frontend/000-frontend-index.md", "link": "#deployment-and-cicd", "text": "Deployment and CI/CD", "status": "Anchor not found: #deployment-and-cicd"}, {"file": "frontend/000-frontend-index.md", "link": "#advanced-patterns", "text": "Advanced Patterns", "status": "Anchor not found: #advanced-patterns"}, {"file": "frontend/000-frontend-index.md", "link": "#troubleshooting", "text": "Troubleshooting", "status": "Anchor not found: #troubleshooting"}, {"file": "frontend/000-frontend-index.md", "link": "#implementation-checklist", "text": "Implementation Checklist", "status": "Anchor not found: #implementation-checklist"}, {"file": "frontend/000-frontend-index.md", "link": "#quick-start", "text": "Quick Start", "status": "Anchor not found: #quick-start"}, {"file": "frontend/000-frontend-index.md", "link": "#best-practices", "text": "Best Practices", "status": "Anchor not found: #best-practices"}, {"file": "frontend/000-frontend-index.md", "link": "#additional-resources", "text": "Additional Resources", "status": "Anchor not found: #additional-resources"}, {"file": "frontend/140-accessibility-wcag-guide.md", "link": "#wcag-21-aa-requirements", "text": "WCAG 2.1 AA Requirements", "status": "Anchor not found: #wcag-21-aa-requirements"}, {"file": "frontend/140-accessibility-wcag-guide.md", "link": "#interactive-components", "text": "Interactive Components", "status": "Anchor not found: #interactive-components"}, {"file": "frontend/140-accessibility-wcag-guide.md", "link": "#media-accessibility", "text": "Media Accessibility", "status": "Anchor not found: #media-accessibility"}, {"file": "frontend/140-accessibility-wcag-guide.md", "link": "#accessibility-tools", "text": "Accessibility Tools", "status": "Anchor not found: #accessibility-tools"}, {"file": "testing/030-feature-testing-guide.md", "link": "#authentication--authorization-testing", "text": "Authentication & Authorization Testing", "status": "Anchor not found: #authentication--authorization-testing"}, {"file": "testing/quality/documentation-quality-validation.md", "link": "020-unit-testing-guide.md#model-testing", "text": "Unit Testing Guide", "status": "File not found: testing/quality/020-unit-testing-guide.md"}, {"file": "testing/quality/documentation-quality-validation.md", "link": "030-feature-testing-guide.md", "text": "Feature Testing Guide", "status": "File not found: testing/quality/030-feature-testing-guide.md"}, {"file": "testing/quality/documentation-quality-validation.md", "link": "050-test-data-management.md", "text": "Test Data Management", "status": "File not found: testing/quality/050-test-data-management.md"}, {"file": "testing/quality/documentation-quality-validation.md", "link": "000-testing-index.md", "text": "Testing Documentation", "status": "File not found: testing/quality/000-testing-index.md"}, {"file": "filament/README.md", "link": "#authentication--authorization", "text": "Authentication & Authorization", "status": "Anchor not found: #authentication--authorization"}, {"file": "filament/README.md", "link": "#performance--security", "text": "Performance & Security", "status": "Anchor not found: #performance--security"}, {"file": "filament/README.md", "link": "#wcag-21-aa-accessibility", "text": "WCAG 2.1 AA Accessibility", "status": "Anchor not found: #wcag-21-aa-accessibility"}, {"file": "filament/README.md", "link": "models/010-model-integration.md", "text": "Model Integration", "status": "File not found: filament/models/010-model-integration.md"}, {"file": "filament/README.md", "link": "models/020-relationship-handling.md", "text": "Relationship Handling", "status": "File not found: filament/models/020-relationship-handling.md"}, {"file": "filament/README.md", "link": "models/030-validation-rules.md", "text": "Validation Rules", "status": "File not found: filament/models/030-validation-rules.md"}, {"file": "filament/README.md", "link": "models/040-scopes-filters.md", "text": "Scopes and Filters", "status": "File not found: filament/models/040-scopes-filters.md"}, {"file": "filament/README.md", "link": "testing/010-resource-testing.md", "text": "Resource Testing", "status": "File not found: filament/testing/010-resource-testing.md"}, {"file": "filament/README.md", "link": "testing/020-feature-testing.md", "text": "Feature Testing", "status": "File not found: filament/testing/020-feature-testing.md"}, {"file": "filament/README.md", "link": "testing/030-authorization-testing.md", "text": "Authorization Testing", "status": "File not found: filament/testing/030-authorization-testing.md"}, {"file": "filament/README.md", "link": "testing/040-performance-testing.md", "text": "Performance Testing", "status": "File not found: filament/testing/040-performance-testing.md"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#form-configuration", "text": "Form Configuration", "status": "Anchor not found: #form-configuration"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#table-configuration", "text": "Table Configuration", "status": "Anchor not found: #table-configuration"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#complex-relationships", "text": "Complex Relationships", "status": "Anchor not found: #complex-relationships"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#album-relationship", "text": "Album Relationship", "status": "Anchor not found: #album-relationship"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#media-type-relationship", "text": "Media Type Relationship", "status": "Anchor not found: #media-type-relationship"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#invoice-lines-relationship", "text": "Invoice Lines Relationship", "status": "Anchor not found: #invoice-lines-relationship"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#audio-file-management", "text": "Audio File Management", "status": "Anchor not found: #audio-file-management"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#pricing-and-sales", "text": "Pricing and Sales", "status": "Anchor not found: #pricing-and-sales"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#playlist-integration", "text": "Playlist Integration", "status": "Anchor not found: #playlist-integration"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#business-logic", "text": "Business Logic", "status": "Anchor not found: #business-logic"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#track-validation", "text": "Track Validation", "status": "Anchor not found: #track-validation"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#duration-handling", "text": "Duration Handling", "status": "Anchor not found: #duration-handling"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#sales-analytics", "text": "Sales Analytics", "status": "Anchor not found: #sales-analytics"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#authorization", "text": "Authorization", "status": "Anchor not found: #authorization"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#testing", "text": "Testing", "status": "Anchor not found: #testing"}, {"file": "filament/resources/120-relationship-managers.md", "link": "110-users-resource.md", "text": "Users Resource", "status": "File not found: filament/resources/110-users-resource.md"}, {"file": "filament/resources/120-relationship-managers.md", "link": "130-form-components.md", "text": "Form Components", "status": "File not found: filament/resources/130-form-components.md"}, {"file": "filament/resources/README.md", "link": "050-playlists-resource.md", "text": "Playlists Resource", "status": "File not found: filament/resources/050-playlists-resource.md"}, {"file": "filament/resources/README.md", "link": "060-media-types-resource.md", "text": "Media Types Resource", "status": "File not found: filament/resources/060-media-types-resource.md"}, {"file": "filament/resources/README.md", "link": "070-customers-resource.md", "text": "Customers Resource", "status": "File not found: filament/resources/070-customers-resource.md"}, {"file": "filament/resources/README.md", "link": "080-invoices-resource.md", "text": "Invoices Resource", "status": "File not found: filament/resources/080-invoices-resource.md"}, {"file": "filament/resources/README.md", "link": "090-invoice-lines-resource.md", "text": "Invoice Lines Resource", "status": "File not found: filament/resources/090-invoice-lines-resource.md"}, {"file": "filament/resources/README.md", "link": "100-employees-resource.md", "text": "Employees Resource", "status": "File not found: filament/resources/100-employees-resource.md"}, {"file": "filament/resources/README.md", "link": "110-users-resource.md", "text": "Users Resource", "status": "File not found: filament/resources/110-users-resource.md"}, {"file": "filament/resources/README.md", "link": "130-form-components.md", "text": "Form Components", "status": "File not found: filament/resources/130-form-components.md"}, {"file": "filament/resources/README.md", "link": "140-table-features.md", "text": "Table Features", "status": "File not found: filament/resources/140-table-features.md"}, {"file": "filament/resources/README.md", "link": "150-bulk-operations.md", "text": "Bulk Operations", "status": "File not found: filament/resources/150-bulk-operations.md"}, {"file": "filament/resources/020-albums-resource.md", "link": "#form-configuration", "text": "Form Configuration", "status": "Anchor not found: #form-configuration"}, {"file": "filament/resources/020-albums-resource.md", "link": "#table-configuration", "text": "Table Configuration", "status": "Anchor not found: #table-configuration"}, {"file": "filament/resources/020-albums-resource.md", "link": "#categories-relationship-manager", "text": "Categories Relationship Manager", "status": "Anchor not found: #categories-relationship-manager"}, {"file": "filament/resources/020-albums-resource.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "filament/resources/020-albums-resource.md", "link": "#custom-form-components", "text": "Custom Form Components", "status": "Anchor not found: #custom-form-components"}, {"file": "filament/resources/020-albums-resource.md", "link": "#advanced-table-features", "text": "Advanced Table Features", "status": "Anchor not found: #advanced-table-features"}, {"file": "filament/resources/020-albums-resource.md", "link": "#bulk-operations", "text": "Bulk Operations", "status": "Anchor not found: #bulk-operations"}, {"file": "filament/resources/020-albums-resource.md", "link": "#authorization", "text": "Authorization", "status": "Anchor not found: #authorization"}, {"file": "filament/resources/020-albums-resource.md", "link": "#resource-level-authorization", "text": "Resource-Level Authorization", "status": "Anchor not found: #resource-level-authorization"}, {"file": "filament/resources/020-albums-resource.md", "link": "#field-level-security", "text": "Field-Level Security", "status": "Anchor not found: #field-level-security"}, {"file": "filament/resources/020-albums-resource.md", "link": "#business-logic", "text": "Business Logic", "status": "Anchor not found: #business-logic"}, {"file": "filament/resources/020-albums-resource.md", "link": "#album-validation", "text": "Album Validation", "status": "Anchor not found: #album-validation"}, {"file": "filament/resources/020-albums-resource.md", "link": "#release-date-handling", "text": "Release Date Handling", "status": "Anchor not found: #release-date-handling"}, {"file": "filament/resources/020-albums-resource.md", "link": "#cover-art-management", "text": "Cover Art Management", "status": "Anchor not found: #cover-art-management"}, {"file": "filament/resources/020-albums-resource.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "filament/resources/020-albums-resource.md", "link": "#testing", "text": "Testing", "status": "Anchor not found: #testing"}, {"file": "filament/resources/040-categories-resource.md", "link": "#hierarchical-form-configuration", "text": "Hierarchical Form Configuration", "status": "Anchor not found: #hierarchical-form-configuration"}, {"file": "filament/resources/040-categories-resource.md", "link": "#tree-table-configuration", "text": "Tree Table Configuration", "status": "Anchor not found: #tree-table-configuration"}, {"file": "filament/resources/040-categories-resource.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "filament/resources/040-categories-resource.md", "link": "#polymorphic-relationships", "text": "Polymorphic Relationships", "status": "Anchor not found: #polymorphic-relationships"}, {"file": "filament/resources/040-categories-resource.md", "link": "#categorizable-implementation", "text": "Categorizable Implementation", "status": "Anchor not found: #categorizable-implementation"}, {"file": "filament/resources/040-categories-resource.md", "link": "#multi-model-assignment", "text": "Multi-Model Assignment", "status": "Anchor not found: #multi-model-assignment"}, {"file": "filament/resources/040-categories-resource.md", "link": "#category-types", "text": "Category Types", "status": "Anchor not found: #category-types"}, {"file": "filament/resources/040-categories-resource.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "filament/resources/040-categories-resource.md", "link": "#category-tree-visualization", "text": "Category Tree Visualization", "status": "Anchor not found: #category-tree-visualization"}, {"file": "filament/resources/040-categories-resource.md", "link": "#bulk-category-operations", "text": "Bulk Category Operations", "status": "Anchor not found: #bulk-category-operations"}, {"file": "filament/resources/040-categories-resource.md", "link": "#category-analytics", "text": "Category Analytics", "status": "Anchor not found: #category-analytics"}, {"file": "filament/resources/040-categories-resource.md", "link": "#business-logic", "text": "Business Logic", "status": "Anchor not found: #business-logic"}, {"file": "filament/resources/040-categories-resource.md", "link": "#category-validation", "text": "Category Validation", "status": "Anchor not found: #category-validation"}, {"file": "filament/resources/040-categories-resource.md", "link": "#hierarchy-constraints", "text": "Hierarchy Constraints", "status": "Anchor not found: #hierarchy-constraints"}, {"file": "filament/resources/040-categories-resource.md", "link": "#usage-tracking", "text": "Usage Tracking", "status": "Anchor not found: #usage-tracking"}, {"file": "filament/resources/040-categories-resource.md", "link": "#authorization", "text": "Authorization", "status": "Anchor not found: #authorization"}, {"file": "filament/resources/040-categories-resource.md", "link": "#testing", "text": "Testing", "status": "Anchor not found: #testing"}, {"file": "filament/resources/040-categories-resource.md", "link": "050-playlists-resource.md", "text": "Playlists Resource", "status": "File not found: filament/resources/050-playlists-resource.md"}, {"file": "filament/resources/000-index.md", "link": "#customer-management", "text": "Customer Management", "status": "Anchor not found: #customer-management"}, {"file": "filament/resources/000-index.md", "link": "#sales--invoicing", "text": "Sales & Invoicing", "status": "Anchor not found: #sales--invoicing"}, {"file": "filament/diagrams/020-database-schema.md", "link": "#wcag-21-aa-compliance", "text": "WCAG 2.1 AA Compliance", "status": "Anchor not found: #wcag-21-aa-compliance"}, {"file": "filament/diagrams/020-database-schema.md", "link": "030-relationship-mapping.md", "text": "Relationship Mapping", "status": "File not found: filament/diagrams/030-relationship-mapping.md"}, {"file": "filament/diagrams/060-filament-panel-architecture.md", "link": "070-authentication-flow.md", "text": "Authentication Flow", "status": "File not found: filament/diagrams/070-authentication-flow.md"}, {"file": "filament/diagrams/060-filament-panel-architecture.md", "link": "090-navigation-structure.md", "text": "Navigation Structure", "status": "File not found: filament/diagrams/090-navigation-structure.md"}, {"file": "filament/diagrams/060-filament-panel-architecture.md", "link": "100-user-journey-maps.md", "text": "User Journey Maps", "status": "File not found: filament/diagrams/100-user-journey-maps.md"}, {"file": "filament/diagrams/README.md", "link": "030-relationship-mapping.md", "text": "Relationship Mapping", "status": "File not found: filament/diagrams/030-relationship-mapping.md"}, {"file": "filament/diagrams/README.md", "link": "040-indexing-strategy.md", "text": "Indexing Strategy", "status": "File not found: filament/diagrams/040-indexing-strategy.md"}, {"file": "filament/diagrams/README.md", "link": "070-authentication-flow.md", "text": "Authentication Flow", "status": "File not found: filament/diagrams/070-authentication-flow.md"}, {"file": "filament/diagrams/README.md", "link": "080-data-flow-diagrams.md", "text": "Data Flow Diagrams", "status": "File not found: filament/diagrams/080-data-flow-diagrams.md"}, {"file": "filament/diagrams/README.md", "link": "090-navigation-structure.md", "text": "Navigation Structure", "status": "File not found: filament/diagrams/090-navigation-structure.md"}, {"file": "filament/diagrams/README.md", "link": "100-user-journey-maps.md", "text": "User Journey Maps", "status": "File not found: filament/diagrams/100-user-journey-maps.md"}, {"file": "filament/diagrams/README.md", "link": "110-wireframes.md", "text": "Wireframes", "status": "File not found: filament/diagrams/110-wireframes.md"}, {"file": "filament/diagrams/README.md", "link": "120-accessibility-features.md", "text": "Accessibility Features", "status": "File not found: filament/diagrams/120-accessibility-features.md"}, {"file": "filament/diagrams/010-entity-relationship-diagrams.md", "link": "030-relationship-mapping.md", "text": "Relationship Mapping", "status": "File not found: filament/diagrams/030-relationship-mapping.md"}, {"file": "filament/diagrams/050-system-architecture.md", "link": "070-authentication-flow.md", "text": "Authentication Flow", "status": "File not found: filament/diagrams/070-authentication-flow.md"}, {"file": "filament/diagrams/050-system-architecture.md", "link": "080-data-flow-diagrams.md", "text": "Data Flow Diagrams", "status": "File not found: filament/diagrams/080-data-flow-diagrams.md"}, {"file": "filament/diagrams/000-index.md", "link": "#wcag-21-aa-compliance", "text": "WCAG 2.1 AA Compliance", "status": "Anchor not found: #wcag-21-aa-compliance"}, {"file": "filament/diagrams/000-index.md", "link": "030-relationship-mapping.md", "text": "Relationship Mapping", "status": "File not found: filament/diagrams/030-relationship-mapping.md"}, {"file": "filament/diagrams/000-index.md", "link": "040-indexing-strategy.md", "text": "Indexing Strategy", "status": "File not found: filament/diagrams/040-indexing-strategy.md"}, {"file": "filament/diagrams/000-index.md", "link": "070-authentication-flow.md", "text": "Authentication Flow", "status": "File not found: filament/diagrams/070-authentication-flow.md"}, {"file": "filament/diagrams/000-index.md", "link": "080-data-flow-diagrams.md", "text": "Data Flow Diagrams", "status": "File not found: filament/diagrams/080-data-flow-diagrams.md"}, {"file": "filament/diagrams/000-index.md", "link": "090-user-workflows.md", "text": "User Workflows", "status": "File not found: filament/diagrams/090-user-workflows.md"}, {"file": "filament/diagrams/000-index.md", "link": "100-admin-workflows.md", "text": "Admin Workflows", "status": "File not found: filament/diagrams/100-admin-workflows.md"}, {"file": "filament/diagrams/000-index.md", "link": "110-api-workflows.md", "text": "API Workflows", "status": "File not found: filament/diagrams/110-api-workflows.md"}, {"file": "filament/diagrams/000-index.md", "link": "120-error-handling.md", "text": "Erro<PERSON>", "status": "File not found: filament/diagrams/120-error-handling.md"}, {"file": "filament/features/README.md", "link": "050-custom-pages.md", "text": "Custom Pages", "status": "File not found: filament/features/050-custom-pages.md"}, {"file": "filament/features/README.md", "link": "060-sales-analytics.md", "text": "Sales Analytics", "status": "File not found: filament/features/060-sales-analytics.md"}, {"file": "filament/features/README.md", "link": "070-employee-hierarchy.md", "text": "Employee Hierarchy", "status": "File not found: filament/features/070-employee-hierarchy.md"}, {"file": "filament/features/README.md", "link": "080-music-discovery.md", "text": "Music Discovery", "status": "File not found: filament/features/080-music-discovery.md"}, {"file": "filament/features/README.md", "link": "100-import-export.md", "text": "Import Export", "status": "File not found: filament/features/100-import-export.md"}, {"file": "filament/features/README.md", "link": "110-bulk-operations.md", "text": "Bulk Operations", "status": "File not found: filament/features/110-bulk-operations.md"}, {"file": "filament/features/README.md", "link": "120-custom-actions.md", "text": "Custom Actions", "status": "File not found: filament/features/120-custom-actions.md"}, {"file": "filament/features/README.md", "link": "130-advanced-forms.md", "text": "Advanced Forms", "status": "File not found: filament/features/130-advanced-forms.md"}, {"file": "filament/features/README.md", "link": "140-table-features.md", "text": "Table Features", "status": "File not found: filament/features/140-table-features.md"}, {"file": "filament/features/README.md", "link": "150-relationship-management.md", "text": "Relationship Management", "status": "File not found: filament/features/150-relationship-management.md"}, {"file": "filament/features/README.md", "link": "160-media-library.md", "text": "Media Library", "status": "File not found: filament/features/160-media-library.md"}, {"file": "filament/features/README.md", "link": "170-activity-logging.md", "text": "Activity Logging", "status": "File not found: filament/features/170-activity-logging.md"}, {"file": "filament/features/README.md", "link": "180-notification-system.md", "text": "Notification System", "status": "File not found: filament/features/180-notification-system.md"}, {"file": "filament/features/000-index.md", "link": "#widget-system", "text": "Widget System", "status": "Anchor not found: #widget-system"}, {"file": "filament/features/000-index.md", "link": "#search--navigation", "text": "Search & Navigation", "status": "Anchor not found: #search--navigation"}, {"file": "filament/features/000-index.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "filament/setup/040-navigation-configuration.md", "link": "#navigation-groups-1", "text": "Navigation Groups", "status": "Anchor not found: #navigation-groups-1"}, {"file": "filament/setup/040-navigation-configuration.md", "link": "#analytics--reports", "text": "Analytics & Reports", "status": "Anchor not found: #analytics--reports"}, {"file": "filament/setup/010-panel-configuration.md", "link": "#panel-configuration", "text": "Panel Configuration", "status": "Anchor not found: #panel-configuration"}, {"file": "filament/setup/010-panel-configuration.md", "link": "#middleware-setup", "text": "Middleware Setup", "status": "Anchor not found: #middleware-setup"}, {"file": "filament/setup/010-panel-configuration.md", "link": "#navigation-configuration", "text": "Navigation Configuration", "status": "Anchor not found: #navigation-configuration"}, {"file": "filament/setup/010-panel-configuration.md", "link": "#theme-and-styling", "text": "Theme and Styling", "status": "Anchor not found: #theme-and-styling"}, {"file": "filament/setup/010-panel-configuration.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "filament/setup/010-panel-configuration.md", "link": "#environment-specific-settings", "text": "Environment-Specific Settings", "status": "Anchor not found: #environment-specific-settings"}, {"file": "filament/setup/070-sqlite-optimization.md", "link": "../deployment/040-monitoring.md", "text": "Monitoring", "status": "File not found: filament/deployment/040-monitoring.md"}, {"file": "filament/setup/020-authentication-setup.md", "link": "#custom-authentication-pages", "text": "Custom Authentication Pages", "status": "Anchor not found: #custom-authentication-pages"}, {"file": "filament/setup/000-index.md", "link": "#authentication--security", "text": "Authentication & Security", "status": "Anchor not found: #authentication--security"}, {"file": "filament/setup/000-index.md", "link": "#configuration--optimization", "text": "Configuration & Optimization", "status": "Anchor not found: #configuration--optimization"}, {"file": "filament/models/010-model-architecture.md", "link": "030-casting-patterns.md", "text": "Casting Patterns", "status": "File not found: filament/models/030-casting-patterns.md"}, {"file": "filament/models/010-model-architecture.md", "link": "040-relationship-patterns.md", "text": "Relationship Patterns", "status": "File not found: filament/models/040-relationship-patterns.md"}, {"file": "filament/models/README.md", "link": "030-casting-patterns.md", "text": "Casting Patterns", "status": "File not found: filament/models/030-casting-patterns.md"}, {"file": "filament/models/README.md", "link": "040-relationship-patterns.md", "text": "Relationship Patterns", "status": "File not found: filament/models/040-relationship-patterns.md"}, {"file": "filament/models/README.md", "link": "060-polymorphic-models.md", "text": "Polymorphic Models", "status": "File not found: filament/models/060-polymorphic-models.md"}, {"file": "filament/models/README.md", "link": "070-user-stamps.md", "text": "User Stamps", "status": "File not found: filament/models/070-user-stamps.md"}, {"file": "filament/models/README.md", "link": "080-soft-deletes.md", "text": "Soft Deletes", "status": "File not found: filament/models/080-soft-deletes.md"}, {"file": "filament/models/README.md", "link": "090-model-factories.md", "text": "Model Factories", "status": "File not found: filament/models/090-model-factories.md"}, {"file": "filament/models/README.md", "link": "100-model-observers.md", "text": "Model Observers", "status": "File not found: filament/models/100-model-observers.md"}, {"file": "filament/models/README.md", "link": "110-model-policies.md", "text": "Model Policies", "status": "File not found: filament/models/110-model-policies.md"}, {"file": "filament/models/README.md", "link": "120-model-scopes.md", "text": "<PERSON> Scopes", "status": "File not found: filament/models/120-model-scopes.md"}, {"file": "filament/models/020-required-traits.md", "link": "030-casting-patterns.md", "text": "Casting Patterns", "status": "File not found: filament/models/030-casting-patterns.md"}, {"file": "filament/models/050-hierarchical-models.md", "link": "060-polymorphic-models.md", "text": "Polymorphic Models", "status": "File not found: filament/models/060-polymorphic-models.md"}, {"file": "filament/models/000-index.md", "link": "060-categorizable-trait.md", "text": "Categorizable Trait", "status": "File not found: filament/models/060-categorizable-trait.md"}, {"file": "filament/models/000-index.md", "link": "070-user-stamps.md", "text": "User Stamps", "status": "File not found: filament/models/070-user-stamps.md"}, {"file": "filament/models/000-index.md", "link": "080-secondary-keys.md", "text": "Secondary Keys", "status": "File not found: filament/models/080-secondary-keys.md"}, {"file": "filament/models/000-index.md", "link": "090-category-management.md", "text": "Category Management", "status": "File not found: filament/models/090-category-management.md"}, {"file": "filament/models/000-index.md", "link": "100-tree-operations.md", "text": "Tree Operations", "status": "File not found: filament/models/100-tree-operations.md"}, {"file": "filament/models/000-index.md", "link": "110-performance-optimization.md", "text": "Performance Optimization", "status": "File not found: filament/models/110-performance-optimization.md"}, {"file": "filament/models/000-index.md", "link": "120-scopes-filters.md", "text": "Scopes and Filters", "status": "File not found: filament/models/120-scopes-filters.md"}, {"file": "filament/models/000-index.md", "link": "130-accessors-mutators.md", "text": "Accessors and Mutators", "status": "File not found: filament/models/130-accessors-mutators.md"}, {"file": "filament/models/000-index.md", "link": "140-model-events.md", "text": "Model Events", "status": "File not found: filament/models/140-model-events.md"}, {"file": "filament/models/000-index.md", "link": "150-custom-methods.md", "text": "Custom Methods", "status": "File not found: filament/models/150-custom-methods.md"}, {"file": "filament/testing/070-table-testing.md", "link": "#column-configuration-testing", "text": "Column Configuration Testing", "status": "Anchor not found: #column-configuration-testing"}, {"file": "filament/testing/070-table-testing.md", "link": "#bulk-operations-testing", "text": "Bulk Operations Testing", "status": "Anchor not found: #bulk-operations-testing"}, {"file": "filament/testing/070-table-testing.md", "link": "#accessibility-testing", "text": "Accessibility Testing", "status": "Anchor not found: #accessibility-testing"}, {"file": "filament/testing/150-accessibility-testing.md", "link": "#wcag-21-aa-compliance-testing", "text": "WCAG 2.1 AA Compliance Testing", "status": "Anchor not found: #wcag-21-aa-compliance-testing"}, {"file": "filament/testing/040-ci-integration.md", "link": "120-performance-testing.md", "text": "Performance Testing", "status": "File not found: filament/testing/120-performance-testing.md"}, {"file": "filament/testing/040-ci-integration.md", "link": "150-security-testing.md", "text": "Security Testing", "status": "File not found: filament/testing/150-security-testing.md"}, {"file": "filament/testing/040-ci-integration.md", "link": "040-resource-testing.md", "text": "Resource Testing", "status": "File not found: filament/testing/040-resource-testing.md"}, {"file": "filament/testing/030-test-data-management.md", "link": "040-resource-testing.md", "text": "Resource Testing", "status": "File not found: filament/testing/040-resource-testing.md"}, {"file": "filament/testing/030-test-data-management.md", "link": "120-performance-testing.md", "text": "Performance Testing", "status": "File not found: filament/testing/120-performance-testing.md"}, {"file": "filament/testing/030-test-data-management.md", "link": "110-database-testing.md", "text": "Database Testing", "status": "File not found: filament/testing/110-database-testing.md"}, {"file": "filament/testing/020-test-environment-setup.md", "link": "040-resource-testing.md", "text": "Resource Testing", "status": "File not found: filament/testing/040-resource-testing.md"}, {"file": "filament/testing/020-test-environment-setup.md", "link": "120-performance-testing.md", "text": "Performance Testing", "status": "File not found: filament/testing/120-performance-testing.md"}, {"file": "filament/testing/050-resource-testing.md", "link": "#authorization-testing", "text": "Authorization Testing", "status": "Anchor not found: #authorization-testing"}, {"file": "filament/testing/050-resource-testing.md", "link": "#validation-testing", "text": "Validation Testing", "status": "Anchor not found: #validation-testing"}, {"file": "filament/testing/050-resource-testing.md", "link": "#relationship-testing", "text": "Relationship Testing", "status": "Anchor not found: #relationship-testing"}, {"file": "filament/testing/050-resource-testing.md", "link": "#advanced-feature-testing", "text": "Advanced Feature Testing", "status": "Anchor not found: #advanced-feature-testing"}, {"file": "filament/testing/050-resource-testing.md", "link": "#performance-testing", "text": "Performance Testing", "status": "Anchor not found: #performance-testing"}, {"file": "filament/testing/060-form-testing.md", "link": "../../../testing/020-unit-testing-guide.md", "text": "Validation Testing", "status": "Path outside base directory: .ai/guides/chinook/filament/testing/../../../testing/020-unit-testing-guide.md"}, {"file": "filament/testing/110-api-testing.md", "link": "#error-handling-testing", "text": "Error Handling Testing", "status": "Anchor not found: #error-handling-testing"}, {"file": "filament/testing/110-api-testing.md", "link": "#integration-testing", "text": "Integration Testing", "status": "Anchor not found: #integration-testing"}, {"file": "filament/testing/010-testing-strategy.md", "link": "040-resource-testing.md", "text": "Resource Testing", "status": "File not found: filament/testing/040-resource-testing.md"}, {"file": "filament/testing/010-testing-strategy.md", "link": "150-performance-testing.md", "text": "Performance Testing", "status": "File not found: filament/testing/150-performance-testing.md"}, {"file": "filament/testing/010-testing-strategy.md", "link": "180-browser-testing.md", "text": "Browser Testing", "status": "File not found: filament/testing/180-browser-testing.md"}, {"file": "filament/deployment/040-ssl-configuration.md", "link": "#lets-encrypt-setup", "text": "Let's Encrypt Setup", "status": "Anchor not found: #lets-encrypt-setup"}, {"file": "filament/deployment/030-security-hardening.md", "link": "#csrf-protection", "text": "CSRF Protection", "status": "Anchor not found: #csrf-protection"}, {"file": "filament/deployment/030-security-hardening.md", "link": "#input-validation", "text": "Input Validation", "status": "Anchor not found: #input-validation"}, {"file": "filament/deployment/030-security-hardening.md", "link": "#monitoring-and-logging", "text": "Monitoring and Logging", "status": "Anchor not found: #monitoring-and-logging"}, {"file": "filament/deployment/030-security-hardening.md", "link": "#security-auditing", "text": "Security Auditing", "status": "Anchor not found: #security-auditing"}, {"file": "filament/deployment/150-performance-optimization-guide.md", "link": "#monitoring--profiling", "text": "Monitoring & Profiling", "status": "Anchor not found: #monitoring--profiling"}, {"file": "filament/deployment/150-performance-optimization-guide.md", "link": "140-browser-testing.md", "text": "Browser Testing Guide", "status": "File not found: filament/deployment/140-browser-testing.md"}, {"file": "filament/deployment/150-performance-optimization-guide.md", "link": "160-scaling-strategies.md", "text": "Scaling Strategies Guide", "status": "File not found: filament/deployment/160-scaling-strategies.md"}, {"file": "filament/deployment/README.md", "link": "090-monitoring-setup.md", "text": "Monitoring Setup", "status": "File not found: filament/deployment/090-monitoring-setup.md"}, {"file": "filament/deployment/README.md", "link": "100-logging-configuration.md", "text": "Logging Configuration", "status": "File not found: filament/deployment/100-logging-configuration.md"}, {"file": "filament/deployment/README.md", "link": "110-backup-strategy.md", "text": "Backup Strategy", "status": "File not found: filament/deployment/110-backup-strategy.md"}, {"file": "filament/deployment/README.md", "link": "120-maintenance-procedures.md", "text": "Maintenance Procedures", "status": "File not found: filament/deployment/120-maintenance-procedures.md"}, {"file": "filament/deployment/README.md", "link": "130-cicd-pipeline.md", "text": "CI/CD Pipeline", "status": "File not found: filament/deployment/130-cicd-pipeline.md"}, {"file": "filament/deployment/README.md", "link": "140-docker-deployment.md", "text": "Docker Deployment", "status": "File not found: filament/deployment/140-docker-deployment.md"}, {"file": "filament/deployment/README.md", "link": "150-cloud-deployment.md", "text": "Cloud Deployment", "status": "File not found: filament/deployment/150-cloud-deployment.md"}, {"file": "filament/deployment/README.md", "link": "160-scaling-strategies.md", "text": "Scaling Strategies", "status": "File not found: filament/deployment/160-scaling-strategies.md"}, {"file": "filament/deployment/060-database-optimization.md", "link": "#backup--recovery", "text": "Backup & Recovery", "status": "Anchor not found: #backup--recovery"}, {"file": "filament/deployment/060-database-optimization.md", "link": "#monitoring--maintenance", "text": "Monitoring & Maintenance", "status": "Anchor not found: #monitoring--maintenance"}, {"file": "filament/deployment/050-performance-optimization.md", "link": "090-monitoring-setup.md", "text": "Monitoring Setup", "status": "File not found: filament/deployment/090-monitoring-setup.md"}, {"file": "filament/deployment/020-server-configuration.md", "link": "#ssl-tls-configuration", "text": "SSL/TLS Configuration", "status": "Anchor not found: #ssl-tls-configuration"}, {"file": "filament/deployment/020-server-configuration.md", "link": "090-monitoring-setup.md", "text": "Monitoring Setup", "status": "File not found: filament/deployment/090-monitoring-setup.md"}, {"file": "filament/deployment/010-production-environment.md", "link": "090-monitoring-setup.md", "text": "Monitoring Setup", "status": "File not found: filament/deployment/090-monitoring-setup.md"}, {"file": "filament/deployment/000-index.md", "link": "#monitoring--maintenance", "text": "Monitoring & Maintenance", "status": "Anchor not found: #monitoring--maintenance"}, {"file": "filament/deployment/000-index.md", "link": "090-monitoring-setup.md", "text": "Monitoring Setup", "status": "File not found: filament/deployment/090-monitoring-setup.md"}, {"file": "filament/deployment/000-index.md", "link": "100-logging-configuration.md", "text": "Logging Configuration", "status": "File not found: filament/deployment/100-logging-configuration.md"}, {"file": "filament/deployment/000-index.md", "link": "110-backup-strategy.md", "text": "Backup Strategy", "status": "File not found: filament/deployment/110-backup-strategy.md"}, {"file": "filament/deployment/000-index.md", "link": "120-maintenance-procedures.md", "text": "Maintenance Procedures", "status": "File not found: filament/deployment/120-maintenance-procedures.md"}, {"file": "filament/deployment/000-index.md", "link": "130-cicd-pipeline.md", "text": "CI/CD Pipeline", "status": "File not found: filament/deployment/130-cicd-pipeline.md"}, {"file": "filament/deployment/000-index.md", "link": "140-docker-deployment.md", "text": "Docker Deployment", "status": "File not found: filament/deployment/140-docker-deployment.md"}, {"file": "filament/deployment/000-index.md", "link": "150-cloud-deployment.md", "text": "Cloud Deployment", "status": "File not found: filament/deployment/150-cloud-deployment.md"}, {"file": "filament/deployment/000-index.md", "link": "160-scaling-strategies.md", "text": "Scaling Strategies", "status": "File not found: filament/deployment/160-scaling-strategies.md"}, {"file": "filament/deployment/080-caching-strategy.md", "link": "#session--user-caching", "text": "Session & User Caching", "status": "Anchor not found: #session--user-caching"}, {"file": "filament/deployment/080-caching-strategy.md", "link": "#file--asset-caching", "text": "File & Asset Caching", "status": "Anchor not found: #file--asset-caching"}, {"file": "filament/deployment/080-caching-strategy.md", "link": "#monitoring--optimization", "text": "Monitoring & Optimization", "status": "Anchor not found: #monitoring--optimization"}, {"file": "filament/deployment/080-caching-strategy.md", "link": "090-monitoring-setup.md", "text": "Monitoring Setup Guide", "status": "File not found: filament/deployment/090-monitoring-setup.md"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#12-configuration-publishing", "text": "1.2. Configuration Publishing", "status": "Anchor not found: #12-configuration-publishing"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#13-environment-configuration", "text": "1.3. Environment Configuration", "status": "Anchor not found: #13-environment-configuration"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#authorization--security", "text": "Authorization & Security", "status": "Anchor not found: #authorization--security"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#21-gate-based-authorization", "text": "2.1. Gate-Based Authorization", "status": "Anchor not found: #21-gate-based-authorization"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#22-environment-specific-access", "text": "2.2. Environment-Specific Access", "status": "Anchor not found: #22-environment-specific-access"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#23-ip-whitelisting", "text": "2.3. IP Whitelisting", "status": "Anchor not found: #23-ip-whitelisting"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#31-watcher-configuration", "text": "3.1. Watcher Configuration", "status": "Anchor not found: #31-watcher-configuration"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#32-filtering--sampling", "text": "3.2. Fi<PERSON><PERSON> & Sam<PERSON>", "status": "Anchor not found: #32-filtering--sampling"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#33-performance-impact-mitigation", "text": "3.3. Performance Impact Mitigation", "status": "Anchor not found: #33-performance-impact-mitigation"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#data-pruning--storage-management", "text": "Data Pruning & Storage Management", "status": "Anchor not found: #data-pruning--storage-management"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#41-automated-pruning", "text": "4.1. Automated Pruning", "status": "Anchor not found: #41-automated-pruning"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#42-storage-optimization", "text": "4.2. Storage Optimization", "status": "Anchor not found: #42-storage-optimization"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#43-custom-retention-policies", "text": "4.3. Custom Retention Policies", "status": "Anchor not found: #43-custom-retention-policies"}, {"file": "packages/130-spatie-laravel-settings-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/130-spatie-laravel-settings-guide.md", "link": "120-nn<PERSON><PERSON>-world-guide.md", "text": "NNJeim World Guide", "status": "File not found: packages/120-nnjeim-world-guide.md"}, {"file": "packages/100-spatie-media-library-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/100-spatie-media-library-guide.md", "link": "#file-conversions--processing", "text": "File Conversions & Processing", "status": "Anchor not found: #file-conversions--processing"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#12-configuration-publishing", "text": "1.2. Configuration Publishing", "status": "Anchor not found: #12-configuration-publishing"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#13-environment-setup", "text": "1.3. Environment Setup", "status": "Anchor not found: #13-environment-setup"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#21-basic-dashboard-setup", "text": "2.1. Basic Dashboard Setup", "status": "Anchor not found: #21-basic-dashboard-setup"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#22-authentication--authorization", "text": "2.2. Authentication & Authorization", "status": "Anchor not found: #22-authentication--authorization"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#23-custom-dashboard-views", "text": "2.3. Custom Dashboard Views", "status": "Anchor not found: #23-custom-dashboard-views"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#31-queue-worker-settings", "text": "3.1. <PERSON><PERSON> Worker Settings", "status": "Anchor not found: #31-queue-worker-settings"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#32-supervisor-configuration", "text": "3.2. Supervisor Configuration", "status": "Anchor not found: #32-supervisor-configuration"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#33-auto-scaling-setup", "text": "3.3. Auto-Scaling Setup", "status": "Anchor not found: #33-auto-scaling-setup"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#41-horizon-watcher-integration", "text": "4.1. Horizon Watcher Integration", "status": "Anchor not found: #41-horizon-watcher-integration"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#42-custom-metrics-collection", "text": "4.2. Custom Metrics Collection", "status": "Anchor not found: #42-custom-metrics-collection"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#43-alert-configuration", "text": "4.3. <PERSON><PERSON>gu<PERSON>", "status": "Anchor not found: #43-alert-configuration"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#performance-tuning", "text": "Performance Tuning", "status": "Anchor not found: #performance-tuning"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#integration-strategies", "text": "Integration Strategies", "status": "Anchor not found: #integration-strategies"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#best-practices", "text": "Best Practices", "status": "Anchor not found: #best-practices"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#troubleshooting", "text": "Troubleshooting", "status": "Anchor not found: #troubleshooting"}, {"file": "packages/140-laravel-optimize-database-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/140-laravel-optimize-database-guide.md", "link": "#query-performance-monitoring", "text": "Query Performance Monitoring", "status": "Anchor not found: #query-performance-monitoring"}, {"file": "packages/140-laravel-optimize-database-guide.md", "link": "#sqlite-specific-optimizations", "text": "SQLite-Specific Optimizations", "status": "Anchor not found: #sqlite-specific-optimizations"}, {"file": "packages/140-laravel-optimize-database-guide.md", "link": "#performance-testing", "text": "Performance Testing", "status": "Anchor not found: #performance-testing"}, {"file": "packages/140-laravel-optimize-database-guide.md", "link": "#production-monitoring", "text": "Production Monitoring", "status": "Anchor not found: #production-monitoring"}, {"file": "packages/140-laravel-optimize-database-guide.md", "link": "#troubleshooting", "text": "Troubleshooting", "status": "Anchor not found: #troubleshooting"}, {"file": "packages/140-laravel-optimize-database-guide.md", "link": "#best-practices", "text": "Best Practices", "status": "Anchor not found: #best-practices"}, {"file": "packages/140-laravel-optimize-database-guide.md", "link": "../testing/010-pest-testing-guide.md", "text": "Modern Testing with Pest Guide", "status": "File not found: testing/010-pest-testing-guide.md"}, {"file": "packages/090-laravel-workos-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/090-laravel-workos-guide.md", "link": "#monitoring--troubleshooting", "text": "Monitoring & Troubleshooting", "status": "Anchor not found: #monitoring--troubleshooting"}, {"file": "packages/000-packages-index.md", "link": "#backup--monitoring", "text": "Backup & Monitoring", "status": "Anchor not found: #backup--monitoring"}, {"file": "packages/000-packages-index.md", "link": "#performance--optimization", "text": "Performance & Optimization", "status": "Anchor not found: #performance--optimization"}, {"file": "packages/000-packages-index.md", "link": "#1-<PERSON><PERSON><PERSON>-backup", "text": "1. <PERSON><PERSON>", "status": "Anchor not found: #1-la<PERSON>l-backup"}, {"file": "packages/000-packages-index.md", "link": "#2-laravel-pulse", "text": "2. <PERSON><PERSON>", "status": "Anchor not found: #2-laravel-pulse"}, {"file": "packages/000-packages-index.md", "link": "#3-laravel-telescope", "text": "3. <PERSON><PERSON> Teles<PERSON>", "status": "Anchor not found: #3-la<PERSON>l-telescope"}, {"file": "packages/000-packages-index.md", "link": "#4-laravel-octane-with-frankenphp", "text": "4. <PERSON><PERSON> with FrankenPHP", "status": "Anchor not found: #4-laravel-octane-with-frankenphp"}, {"file": "packages/000-packages-index.md", "link": "#5-laravel-horizon", "text": "5. <PERSON><PERSON>", "status": "Anchor not found: #5-laravel-horizon"}, {"file": "packages/000-packages-index.md", "link": "#6-laravel-data", "text": "6. <PERSON><PERSON>", "status": "Anchor not found: #6-laravel-data"}, {"file": "packages/000-packages-index.md", "link": "#7-laravel-fractal", "text": "7. <PERSON><PERSON>", "status": "Anchor not found: #7-laravel-fractal"}, {"file": "packages/000-packages-index.md", "link": "#8-laravel-sanctum", "text": "8. <PERSON><PERSON>", "status": "Anchor not found: #8-laravel-sanctum"}, {"file": "packages/000-packages-index.md", "link": "#9-<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "text": "9. <PERSON><PERSON>", "status": "Anchor not found: #9-<PERSON><PERSON><PERSON>-<PERSON><PERSON>"}, {"file": "packages/000-packages-index.md", "link": "#10-laravel-query-builder", "text": "10. <PERSON><PERSON> Query Builder", "status": "Anchor not found: #10-laravel-query-builder"}, {"file": "packages/000-packages-index.md", "link": "#11-spatie-comments", "text": "11. <PERSON><PERSON>", "status": "Anchor not found: #11-spatie-comments"}, {"file": "packages/000-packages-index.md", "link": "#12-laravel-folio", "text": "12. <PERSON><PERSON>", "status": "Anchor not found: #12-laravel-folio"}, {"file": "packages/000-packages-index.md", "link": "#13-nn<PERSON><PERSON>-world", "text": "13. NNJeim World", "status": "Anchor not found: #13-nn<PERSON><PERSON>-world"}, {"file": "packages/000-packages-index.md", "link": "#14-laravel-database-optimization", "text": "14. Laravel Database Optimization", "status": "Anchor not found: #14-laravel-database-optimization"}, {"file": "packages/000-packages-index.md", "link": "#15-enhanced-spatie-activitylog", "text": "15. Enhanced Spatie ActivityLog", "status": "Anchor not found: #15-enhanced-spatie-activitylog"}, {"file": "packages/000-packages-index.md", "link": "140-laravel-database-optimization-guide.md", "text": "Laravel Database Optimization Guide", "status": "File not found: packages/140-laravel-database-optimization-guide.md"}, {"file": "packages/000-packages-index.md", "link": "150-enhanced-spatie-activitylog-guide.md", "text": "Enhanced Spatie ActivityLog Guide", "status": "File not found: packages/150-enhanced-spatie-activitylog-guide.md"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#12-configuration-publishing", "text": "1.2. Configuration Publishing", "status": "Anchor not found: #12-configuration-publishing"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#13-environment-configuration", "text": "1.3. Environment Configuration", "status": "Anchor not found: #13-environment-configuration"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#21-local-storage-setup", "text": "2.1. Local Storage Setup", "status": "Anchor not found: #21-local-storage-setup"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#22-aws-s3-configuration", "text": "2.2. AWS S3 Configuration", "status": "Anchor not found: #22-aws-s3-configuration"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#23-google-cloud-storage", "text": "2.3. Google Cloud Storage", "status": "Anchor not found: #23-google-cloud-storage"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#24-multi-destination-strategy", "text": "2.4. Multi-Destination Strategy", "status": "Anchor not found: #24-multi-destination-strategy"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#31-basic-backup-settings", "text": "3.1. Basic Backup Settings", "status": "Anchor not found: #31-basic-backup-settings"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#32-database-backup-options", "text": "3.2. Database Backup Options", "status": "Anchor not found: #32-database-backup-options"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#33-file-backup-configuration", "text": "3.3. File Backup Configuration", "status": "Anchor not found: #33-file-backup-configuration"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#34-exclusion-patterns", "text": "3.4. <PERSON><PERSON><PERSON><PERSON>", "status": "Anchor not found: #34-exclusion-patterns"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#41-laravel-task-scheduler", "text": "4.1. <PERSON><PERSON> Scheduler", "status": "Anchor not found: #41-laravel-task-scheduler"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#42-cron-configuration", "text": "4.2. <PERSON>ron Configuration", "status": "Anchor not found: #42-cron-configuration"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#43-backup-frequency-strategies", "text": "4.3. <PERSON><PERSON> Frequency Strategies", "status": "Anchor not found: #43-backup-frequency-strategies"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#51-email-notifications", "text": "5.1. Email Notifications", "status": "Anchor not found: #51-email-notifications"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#52-slack-integration", "text": "5.2. <PERSON><PERSON><PERSON>", "status": "Anchor not found: #52-slack-integration"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#53-custom-notification-channels", "text": "5.3. Custom Notification Channels", "status": "Anchor not found: #53-custom-notification-channels"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#monitoring--health-checks", "text": "Monitoring & Health Checks", "status": "Anchor not found: #monitoring--health-checks"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#12-frankenphp-server-setup", "text": "1.2. FrankenPHP Server Setup", "status": "Anchor not found: #12-frankenphp-server-setup"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#13-environment-configuration", "text": "1.3. Environment Configuration", "status": "Anchor not found: #13-environment-configuration"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#21-basic-server-settings", "text": "2.1. Basic Server Settings", "status": "Anchor not found: #21-basic-server-settings"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#22-performance-optimization", "text": "2.2. Performance Optimization", "status": "Anchor not found: #22-performance-optimization"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#23-ssltls-configuration", "text": "2.3. SSL/TLS Configuration", "status": "Anchor not found: #23-ssltls-configuration"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#31-memory-leak-prevention", "text": "3.1. Memory Leak Prevention", "status": "Anchor not found: #31-memory-leak-prevention"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#32-resource-optimization", "text": "3.2. Resource Optimization", "status": "Anchor not found: #32-resource-optimization"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#33-garbage-collection-tuning", "text": "3.3. Garbage Collection Tuning", "status": "Anchor not found: #33-garbage-collection-tuning"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#41-docker-configuration", "text": "4.1. <PERSON><PERSON> Configuration", "status": "Anchor not found: #41-docker-configuration"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#42-load-balancing", "text": "4.2. <PERSON><PERSON>", "status": "Anchor not found: #42-load-balancing"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#43-scaling-strategies", "text": "4.3. Scaling Strategies", "status": "Anchor not found: #43-scaling-strategies"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#monitoring--troubleshooting", "text": "Monitoring & Troubleshooting", "status": "Anchor not found: #monitoring--troubleshooting"}, {"file": "packages/120-spatie-activitylog-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/120-spatie-activitylog-guide.md", "link": "#security--compliance", "text": "Security & Compliance", "status": "Anchor not found: #security--compliance"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#12-configuration-publishing", "text": "1.2. Configuration Publishing", "status": "Anchor not found: #12-configuration-publishing"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#13-basic-setup", "text": "1.3. Basic Setup", "status": "Anchor not found: #13-basic-setup"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#21-basic-transformers", "text": "2.1. Basic Transformers", "status": "Anchor not found: #21-basic-transformers"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#22-advanced-transformers", "text": "2.2. Advanced Transformers", "status": "Anchor not found: #22-advanced-transformers"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#23-nested-transformers", "text": "2.3. Nested Transformers", "status": "Anchor not found: #23-nested-transformers"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#31-item-resources", "text": "3.1. Item Resources", "status": "Anchor not found: #31-item-resources"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#32-collection-resources", "text": "3.2. Collection Resources", "status": "Anchor not found: #32-collection-resources"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#33-relationship-handling", "text": "3.3. Relationship Handling", "status": "Anchor not found: #33-relationship-handling"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#pagination--filtering", "text": "Pagination & Filtering", "status": "Anchor not found: #pagination--filtering"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#41-pagination-setup", "text": "4.1. Pa<PERSON>ation Setup", "status": "Anchor not found: #41-pagination-setup"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#42-advanced-filtering", "text": "4.2. Advanced Filtering", "status": "Anchor not found: #42-advanced-filtering"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#43-sorting--ordering", "text": "4.3. Sorting & Ordering", "status": "Anchor not found: #43-sorting--ordering"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#api-response-formatting", "text": "API Response Formatting", "status": "Anchor not found: #api-response-formatting"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#caching-integration", "text": "Caching Integration", "status": "Anchor not found: #caching-integration"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#testing-strategies", "text": "Testing Strategies", "status": "Anchor not found: #testing-strategies"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#best-practices", "text": "Best Practices", "status": "Anchor not found: #best-practices"}, {"file": "packages/090-spatie-tags-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/150-spatie-laravel-translatable-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/150-spatie-laravel-translatable-guide.md", "link": "../integration-patterns.md", "text": "Package Integration Patterns", "status": "File not found: integration-patterns.md"}, {"file": "packages/110-spatie-permission-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#12-api-installation", "text": "1.2. API Installation", "status": "Anchor not found: #12-api-installation"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#13-configuration-setup", "text": "1.3. Configuration Setup", "status": "Anchor not found: #13-configuration-setup"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#21-token-generation", "text": "2.1. Token <PERSON>", "status": "Anchor not found: #21-token-generation"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#22-token-management", "text": "2.2. Token Management", "status": "Anchor not found: #22-token-management"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#23-token-abilities", "text": "2.3. Token Abilities", "status": "Anchor not found: #23-token-abilities"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#31-csrf-protection", "text": "3.1. CSRF Protection", "status": "Anchor not found: #31-csrf-protection"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#32-session-based-auth", "text": "3.2. Session-Based Auth", "status": "Anchor not found: #32-session-based-auth"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#33-frontend-integration", "text": "3.3. Frontend Integration", "status": "Anchor not found: #33-frontend-integration"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#41-mobile-token-flow", "text": "4.1. Mobile Token Flow", "status": "Anchor not found: #41-mobile-token-flow"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#42-device-management", "text": "4.2. Device Management", "status": "Anchor not found: #42-device-management"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#43-push-notifications", "text": "4.3. Push Notifications", "status": "Anchor not found: #43-push-notifications"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#security-best-practices", "text": "Security Best Practices", "status": "Anchor not found: #security-best-practices"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#rate-limiting", "text": "Rate Limiting", "status": "Anchor not found: #rate-limiting"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#testing-strategies", "text": "Testing Strategies", "status": "Anchor not found: #testing-strategies"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#troubleshooting", "text": "Troubleshooting", "status": "Anchor not found: #troubleshooting"}, {"file": "packages/110-spatie-comments-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/110-spatie-comments-guide.md", "link": "#rbac-integration", "text": "RBAC Integration", "status": "Anchor not found: #rbac-integration"}, {"file": "packages/110-spatie-comments-guide.md", "link": "#real-time-updates", "text": "Real-time Updates", "status": "Anchor not found: #real-time-updates"}, {"file": "packages/110-spatie-comments-guide.md", "link": "#spam-prevention", "text": "Spam Prevention", "status": "Anchor not found: #spam-prevention"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#12-database-configuration", "text": "1.2. Database Configuration", "status": "Anchor not found: #12-database-configuration"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#13-environment-setup", "text": "1.3. Environment Setup", "status": "Anchor not found: #13-environment-setup"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#21-basic-dashboard-setup", "text": "2.1. Basic Dashboard Setup", "status": "Anchor not found: #21-basic-dashboard-setup"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#22-custom-dashboard-layouts", "text": "2.2. Custom Dashboard Layouts", "status": "Anchor not found: #22-custom-dashboard-layouts"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#23-authentication--authorization", "text": "2.3. Authentication & Authorization", "status": "Anchor not found: #23-authentication--authorization"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#31-built-in-recorders", "text": "3.1. Built-in Recorders", "status": "Anchor not found: #31-built-in-recorders"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#32-custom-metrics-collection", "text": "3.2. Custom Metrics Collection", "status": "Anchor not found: #32-custom-metrics-collection"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#33-performance-monitoring", "text": "3.3. Performance Monitoring", "status": "Anchor not found: #33-performance-monitoring"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#custom-metrics--cards", "text": "Custom Metrics & Cards", "status": "Anchor not found: #custom-metrics--cards"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#41-creating-custom-recorders", "text": "4.1. Creating Custom Recorders", "status": "Anchor not found: #41-creating-custom-recorders"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#42-building-custom-cards", "text": "4.2. Building Custom Cards", "status": "Anchor not found: #42-building-custom-cards"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#43-business-metrics-integration", "text": "4.3. Business Metrics Integration", "status": "Anchor not found: #43-business-metrics-integration"}, {"file": "packages/140-spatie-laravel-query-builder-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/100-laravel-query-builder-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/100-laravel-query-builder-guide.md", "link": "#sorting--pagination", "text": "Sorting & Pagination", "status": "Anchor not found: #sorting--pagination"}, {"file": "packages/100-laravel-query-builder-guide.md", "link": "#custom-filter-development", "text": "Custom Filter Development", "status": "Anchor not found: #custom-filter-development"}, {"file": "packages/100-laravel-query-builder-guide.md", "link": "#real-world-examples", "text": "Real-World Examples", "status": "Anchor not found: #real-world-examples"}, {"file": "packages/120-laravel-folio-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/120-laravel-folio-guide.md", "link": "#livewire-volt-integration", "text": "Livewire/Volt Integration", "status": "Anchor not found: #livewire-volt-integration"}, {"file": "packages/120-laravel-folio-guide.md", "link": "#production-deployment", "text": "Production Deployment", "status": "Anchor not found: #production-deployment"}, {"file": "packages/120-laravel-folio-guide.md", "link": "#real-world-examples", "text": "Real-World Examples", "status": "Anchor not found: #real-world-examples"}, {"file": "packages/060-laravel-data-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "packages/060-laravel-data-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "packages/060-laravel-data-guide.md", "link": "#12-configuration-publishing", "text": "1.2. Configuration Publishing", "status": "Anchor not found: #12-configuration-publishing"}, {"file": "packages/060-laravel-data-guide.md", "link": "#13-basic-setup", "text": "1.3. Basic Setup", "status": "Anchor not found: #13-basic-setup"}, {"file": "packages/060-laravel-data-guide.md", "link": "#21-basic-dto-creation", "text": "2.1. Basic DTO Creation", "status": "Anchor not found: #21-basic-dto-creation"}, {"file": "packages/060-laravel-data-guide.md", "link": "#22-advanced-dto-features", "text": "2.2. Advanced DTO Features", "status": "Anchor not found: #22-advanced-dto-features"}, {"file": "packages/060-laravel-data-guide.md", "link": "#23-nested-dtos", "text": "2.3. Nested DTOs", "status": "Anchor not found: #23-nested-dtos"}, {"file": "packages/060-laravel-data-guide.md", "link": "#validation--transformation", "text": "Validation & Transformation", "status": "Anchor not found: #validation--transformation"}, {"file": "packages/060-laravel-data-guide.md", "link": "#31-built-in-validation", "text": "3.1. Built-in Validation", "status": "Anchor not found: #31-built-in-validation"}, {"file": "packages/060-laravel-data-guide.md", "link": "#32-custom-validation-rules", "text": "3.2. Custom Validation Rules", "status": "Anchor not found: #32-custom-validation-rules"}, {"file": "packages/060-laravel-data-guide.md", "link": "#33-data-transformation", "text": "3.3. Data Transformation", "status": "Anchor not found: #33-data-transformation"}, {"file": "packages/060-laravel-data-guide.md", "link": "#41-api-resource-integration", "text": "4.1. API Resource Integration", "status": "Anchor not found: #41-api-resource-integration"}, {"file": "packages/060-laravel-data-guide.md", "link": "#42-request-handling", "text": "4.2. Request Handling", "status": "Anchor not found: #42-request-handling"}, {"file": "packages/060-laravel-data-guide.md", "link": "#43-response-formatting", "text": "4.3. Response Formatting", "status": "Anchor not found: #43-response-formatting"}, {"file": "packages/060-laravel-data-guide.md", "link": "#collections--arrays", "text": "Collections & Arrays", "status": "Anchor not found: #collections--arrays"}, {"file": "packages/150-spatie-activitylog-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/150-spatie-activitylog-guide.md", "link": "#security--compliance", "text": "Security & Compliance", "status": "Anchor not found: #security--compliance"}, {"file": "packages/150-spatie-activitylog-guide.md", "link": "#real-time-activity-monitoring", "text": "Real-time Activity Monitoring", "status": "Anchor not found: #real-time-activity-monitoring"}, {"file": "packages/150-spatie-activitylog-guide.md", "link": "#activity-analytics", "text": "Activity Analytics", "status": "Anchor not found: #activity-analytics"}, {"file": "packages/150-spatie-activitylog-guide.md", "link": "#integration-patterns", "text": "Integration Patterns", "status": "Anchor not found: #integration-patterns"}, {"file": "packages/150-spatie-activitylog-guide.md", "link": "#production-deployment", "text": "Production Deployment", "status": "Anchor not found: #production-deployment"}, {"file": "packages/150-spatie-activitylog-guide.md", "link": "../testing/010-pest-testing-guide.md", "text": "Modern Testing with Pest Guide", "status": "File not found: testing/010-pest-testing-guide.md"}, {"file": "packages/150-spatie-activitylog-guide.md", "link": "../development/010-debugbar-guide.md", "text": "Development Debugging Tools Guide", "status": "File not found: development/010-debugbar-guide.md"}, {"file": "packages/130-nnjeim-world-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/130-nnjeim-world-guide.md", "link": "#country-state--city-management", "text": "Country, State & City Management", "status": "Anchor not found: #country-state--city-management"}, {"file": "packages/130-nnjeim-world-guide.md", "link": "#user-profile-integration", "text": "User Profile Integration", "status": "Anchor not found: #user-profile-integration"}, {"file": "packages/130-nnjeim-world-guide.md", "link": "#business-logic-integration", "text": "Business Logic Integration", "status": "Anchor not found: #business-logic-integration"}, {"file": "packages/testing/000-testing-index.md", "link": "../development/000-development-index.md", "text": "Development Tools Index", "status": "File not found: packages/development/000-development-index.md"}, {"file": "packages/testing/010-pest-testing-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/testing/010-pest-testing-guide.md", "link": "#advanced-testing-patterns", "text": "Advanced Testing Patterns", "status": "Anchor not found: #advanced-testing-patterns"}, {"file": "packages/testing/010-pest-testing-guide.md", "link": "#type-coverage", "text": "Type Coverage", "status": "Anchor not found: #type-coverage"}, {"file": "packages/testing/010-pest-testing-guide.md", "link": "#api-testing", "text": "API Testing", "status": "Anchor not found: #api-testing"}, {"file": "packages/testing/010-pest-testing-guide.md", "link": "#livewire-testing", "text": "Livewire Testing", "status": "Anchor not found: #livewire-testing"}, {"file": "packages/testing/010-pest-testing-guide.md", "link": "../development/010-debugbar-guide.md", "text": "Development Debugging Tools Guide", "status": "File not found: packages/development/010-debugbar-guide.md"}, {"file": "packages/testing/010-pest-testing-guide.md", "link": "../development/020-pint-code-quality-guide.md", "text": "Code Quality and Formatting Guide", "status": "File not found: packages/development/020-pint-code-quality-guide.md"}, {"file": "packages/testing/010-pest-testing-guide.md", "link": "../packages/150-spatie-activitylog-guide.md", "text": "Enhanced Spatie ActivityLog Guide", "status": "File not found: packages/packages/150-spatie-activitylog-guide.md"}], "resolved_issues": [], "status": "FAIL", "execution_time": 0.42804813385009766}, {"timestamp": "2025-07-07T20:12:01.162361", "total_files": 143, "total_links": 2808, "broken_links": 456, "success_rate": 83.76068376068376, "critical_files_status": {"000-chinook-index.md": 19, "050-chinook-advanced-features-guide.md": 1, "060-chinook-media-library-guide.md": 0, "070-chinook-hierarchy-comparison-guide.md": 1, "filament/setup/000-index.md": 2, "filament/resources/000-index.md": 2, "packages/000-packages-index.md": 19, "testing/000-testing-index.md": 0}, "new_issues": [{"file": "040-chinook-seeders-guide.md", "link": "#customer-seeder", "text": "Customer Seeder", "status": "Anchor not found: #customer-seeder"}, {"file": "040-chinook-seeders-guide.md", "link": "#playlist-seeder", "text": "Playlist Seeder", "status": "Anchor not found: #playlist-seeder"}, {"file": "040-chinook-seeders-guide.md", "link": "#track-seeder", "text": "Track Seeder", "status": "Anchor not found: #track-seeder"}, {"file": "040-chinook-seeders-guide.md", "link": "#invoice-seeder", "text": "Invoice Seeder", "status": "Anchor not found: #invoice-seeder"}, {"file": "040-chinook-seeders-guide.md", "link": "#invoiceline-seeder", "text": "InvoiceLine Seeder", "status": "Anchor not found: #invoiceline-seeder"}, {"file": "040-chinook-seeders-guide.md", "link": "#playlisttrack-seeder", "text": "Playlist<PERSON><PERSON>", "status": "Anchor not found: #playlisttrack-seeder"}, {"file": "040-chinook-seeders-guide.md", "link": "#database-seeder", "text": "Database Seeder", "status": "Anchor not found: #database-seeder"}, {"file": "README.md", "link": "../../reports/chinook/DOCUMENTATION_AUDIT_REPORT.md", "text": "Documentation Audit Report", "status": "Path outside base directory: .ai/guides/chinook/../../reports/chinook/DOCUMENTATION_AUDIT_REPORT.md"}, {"file": "000-chinook-index.md", "link": "#8-panel-setup--configuration", "text": "8. Panel Setup & Configuration", "status": "Anchor not found: #8-panel-setup--configuration"}, {"file": "000-chinook-index.md", "link": "#9-model-standards--architecture", "text": "9. Model Standards & Architecture", "status": "Anchor not found: #9-model-standards--architecture"}, {"file": "000-chinook-index.md", "link": "#11-advanced-features--widgets", "text": "11. Advanced Features & Widgets", "status": "Anchor not found: #11-advanced-features--widgets"}, {"file": "000-chinook-index.md", "link": "#12-testing--quality-assurance", "text": "12. Testing & Quality Assurance", "status": "Anchor not found: #12-testing--quality-assurance"}, {"file": "000-chinook-index.md", "link": "#13-deployment--production", "text": "13. Deployment & Production", "status": "Anchor not found: #13-deployment--production"}, {"file": "000-chinook-index.md", "link": "#14-visual-documentation--diagrams", "text": "14. Visual Documentation & Diagrams", "status": "Anchor not found: #14-visual-documentation--diagrams"}, {"file": "000-chinook-index.md", "link": "#15-frontend-architecture--patterns", "text": "15. Frontend Architecture & Patterns", "status": "Anchor not found: #15-frontend-architecture--patterns"}, {"file": "000-chinook-index.md", "link": "#16-livewire-volt-integration", "text": "16. Livewire/Volt Integration", "status": "Anchor not found: #16-livewire-volt-integration"}, {"file": "000-chinook-index.md", "link": "#17-performance--accessibility", "text": "17. Performance & Accessibility", "status": "Anchor not found: #17-performance--accessibility"}, {"file": "000-chinook-index.md", "link": "#18-testing--cicd", "text": "18. Testing & CI/CD", "status": "Anchor not found: #18-testing--cicd"}, {"file": "000-chinook-index.md", "link": "#testing--quality-assurance", "text": "Testing & Quality Assurance", "status": "Anchor not found: #testing--quality-assurance"}, {"file": "000-chinook-index.md", "link": "#database--data", "text": "Database & Data", "status": "Anchor not found: #database--data"}, {"file": "000-chinook-index.md", "link": "../../reports/chinook/DOCUMENTATION_AUDIT_REPORT.md", "text": "Documentation Audit Report", "status": "Path outside base directory: .ai/guides/chinook/../../reports/chinook/DOCUMENTATION_AUDIT_REPORT.md"}, {"file": "000-chinook-index.md", "link": "filament/010-panel-setup-guide.md", "text": "Filament Panel Setup", "status": "File not found: filament/010-panel-setup-guide.md"}, {"file": "000-chinook-index.md", "link": "filament/models/010-model-standards-guide.md", "text": "Model Standards", "status": "File not found: filament/models/010-model-standards-guide.md"}, {"file": "000-chinook-index.md", "link": "filament/040-advanced-features-guide.md", "text": "Advanced Features", "status": "File not found: filament/040-advanced-features-guide.md"}, {"file": "000-chinook-index.md", "link": "filament/deployment/010-deployment-guide.md", "text": "Deployment Guide", "status": "File not found: filament/deployment/010-deployment-guide.md"}, {"file": "000-chinook-index.md", "link": "014-visual-documentation-guide.md", "text": "Visual Documentation", "status": "File not found: 014-visual-documentation-guide.md"}, {"file": "000-chinook-index.md", "link": "frontend/160-livewire-volt-integration-guide.md", "text": "Livewire Integration", "status": "File not found: frontend/160-livewire-volt-integration-guide.md"}, {"file": "050-chinook-advanced-features-guide.md", "link": "#rbac-authentication--authorization-flow", "text": "RBAC Authentication & Authorization Flow", "status": "Anchor not found: #rbac-authentication--authorization-flow"}, {"file": "070-chinook-hierarchy-comparison-guide.md", "link": "../../testing/000-testing-index.md", "text": "Testing Guide", "status": "Path outside base directory: .ai/guides/chinook/../../testing/000-testing-index.md"}, {"file": "frontend/190-cicd-integration-guide.md", "link": "200-media-library-enhancement-guide.md", "text": "Media Library Enhancement Guide", "status": "File not found: frontend/200-media-library-enhancement-guide.md"}, {"file": "frontend/140-accessibility-wcag-guide.md", "link": "#wcag-21-aa-requirements", "text": "WCAG 2.1 AA Requirements", "status": "Anchor not found: #wcag-21-aa-requirements"}, {"file": "testing/030-feature-testing-guide.md", "link": "#authentication--authorization-testing", "text": "Authentication & Authorization Testing", "status": "Anchor not found: #authentication--authorization-testing"}, {"file": "testing/quality/documentation-quality-validation.md", "link": "020-unit-testing-guide.md#model-testing", "text": "Unit Testing Guide", "status": "File not found: testing/quality/020-unit-testing-guide.md"}, {"file": "testing/quality/documentation-quality-validation.md", "link": "030-feature-testing-guide.md", "text": "Feature Testing Guide", "status": "File not found: testing/quality/030-feature-testing-guide.md"}, {"file": "testing/quality/documentation-quality-validation.md", "link": "050-test-data-management.md", "text": "Test Data Management", "status": "File not found: testing/quality/050-test-data-management.md"}, {"file": "testing/quality/documentation-quality-validation.md", "link": "000-testing-index.md", "text": "Testing Documentation", "status": "File not found: testing/quality/000-testing-index.md"}, {"file": "filament/README.md", "link": "#authentication--authorization", "text": "Authentication & Authorization", "status": "Anchor not found: #authentication--authorization"}, {"file": "filament/README.md", "link": "#performance--security", "text": "Performance & Security", "status": "Anchor not found: #performance--security"}, {"file": "filament/README.md", "link": "#wcag-21-aa-accessibility", "text": "WCAG 2.1 AA Accessibility", "status": "Anchor not found: #wcag-21-aa-accessibility"}, {"file": "filament/README.md", "link": "models/010-model-integration.md", "text": "Model Integration", "status": "File not found: filament/models/010-model-integration.md"}, {"file": "filament/README.md", "link": "models/020-relationship-handling.md", "text": "Relationship Handling", "status": "File not found: filament/models/020-relationship-handling.md"}, {"file": "filament/README.md", "link": "models/030-validation-rules.md", "text": "Validation Rules", "status": "File not found: filament/models/030-validation-rules.md"}, {"file": "filament/README.md", "link": "models/040-scopes-filters.md", "text": "Scopes and Filters", "status": "File not found: filament/models/040-scopes-filters.md"}, {"file": "filament/README.md", "link": "testing/010-resource-testing.md", "text": "Resource Testing", "status": "File not found: filament/testing/010-resource-testing.md"}, {"file": "filament/README.md", "link": "testing/020-feature-testing.md", "text": "Feature Testing", "status": "File not found: filament/testing/020-feature-testing.md"}, {"file": "filament/README.md", "link": "testing/030-authorization-testing.md", "text": "Authorization Testing", "status": "File not found: filament/testing/030-authorization-testing.md"}, {"file": "filament/README.md", "link": "testing/040-performance-testing.md", "text": "Performance Testing", "status": "File not found: filament/testing/040-performance-testing.md"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#form-configuration", "text": "Form Configuration", "status": "Anchor not found: #form-configuration"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#table-configuration", "text": "Table Configuration", "status": "Anchor not found: #table-configuration"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#complex-relationships", "text": "Complex Relationships", "status": "Anchor not found: #complex-relationships"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#album-relationship", "text": "Album Relationship", "status": "Anchor not found: #album-relationship"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#media-type-relationship", "text": "Media Type Relationship", "status": "Anchor not found: #media-type-relationship"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#invoice-lines-relationship", "text": "Invoice Lines Relationship", "status": "Anchor not found: #invoice-lines-relationship"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#audio-file-management", "text": "Audio File Management", "status": "Anchor not found: #audio-file-management"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#pricing-and-sales", "text": "Pricing and Sales", "status": "Anchor not found: #pricing-and-sales"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#playlist-integration", "text": "Playlist Integration", "status": "Anchor not found: #playlist-integration"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#business-logic", "text": "Business Logic", "status": "Anchor not found: #business-logic"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#track-validation", "text": "Track Validation", "status": "Anchor not found: #track-validation"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#duration-handling", "text": "Duration Handling", "status": "Anchor not found: #duration-handling"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#sales-analytics", "text": "Sales Analytics", "status": "Anchor not found: #sales-analytics"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#authorization", "text": "Authorization", "status": "Anchor not found: #authorization"}, {"file": "filament/resources/030-tracks-resource.md", "link": "#testing", "text": "Testing", "status": "Anchor not found: #testing"}, {"file": "filament/resources/140-bulk-operations.md", "link": "#progress-tracking", "text": "Progress Tracking", "status": "Anchor not found: #progress-tracking"}, {"file": "filament/resources/140-bulk-operations.md", "link": "../performance/bulk-processing.md", "text": "Performance Optimization", "status": "File not found: filament/performance/bulk-processing.md"}, {"file": "filament/resources/140-bulk-operations.md", "link": "../error-handling/bulk-operations.md", "text": "Erro<PERSON>", "status": "File not found: filament/error-handling/bulk-operations.md"}, {"file": "filament/resources/120-form-components.md", "link": "../forms/validation.md", "text": "Validation Strategies", "status": "File not found: filament/forms/validation.md"}, {"file": "filament/resources/120-form-components.md", "link": "../ui/component-library.md", "text": "UI Components", "status": "File not found: filament/ui/component-library.md"}, {"file": "filament/resources/080-invoices-resource.md", "link": "../integrations/payment-gateways.md", "text": "Payment Processing", "status": "File not found: filament/integrations/payment-gateways.md"}, {"file": "filament/resources/080-invoices-resource.md", "link": "../analytics/financial-analytics.md", "text": "Financial Reporting", "status": "File not found: filament/analytics/financial-analytics.md"}, {"file": "filament/resources/120-relationship-managers.md", "link": "130-form-components.md", "text": "Form Components", "status": "File not found: filament/resources/130-form-components.md"}, {"file": "filament/resources/070-customers-resource.md", "link": "../security/privacy-compliance.md", "text": "Privacy and Security", "status": "File not found: filament/security/privacy-compliance.md"}, {"file": "filament/resources/070-customers-resource.md", "link": "../analytics/customer-insights.md", "text": "Customer Analytics", "status": "File not found: filament/analytics/customer-insights.md"}, {"file": "filament/resources/README.md", "link": "130-form-components.md", "text": "Form Components", "status": "File not found: filament/resources/130-form-components.md"}, {"file": "filament/resources/README.md", "link": "140-table-features.md", "text": "Table Features", "status": "File not found: filament/resources/140-table-features.md"}, {"file": "filament/resources/README.md", "link": "150-bulk-operations.md", "text": "Bulk Operations", "status": "File not found: filament/resources/150-bulk-operations.md"}, {"file": "filament/resources/020-albums-resource.md", "link": "#form-configuration", "text": "Form Configuration", "status": "Anchor not found: #form-configuration"}, {"file": "filament/resources/020-albums-resource.md", "link": "#table-configuration", "text": "Table Configuration", "status": "Anchor not found: #table-configuration"}, {"file": "filament/resources/020-albums-resource.md", "link": "#categories-relationship-manager", "text": "Categories Relationship Manager", "status": "Anchor not found: #categories-relationship-manager"}, {"file": "filament/resources/020-albums-resource.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "filament/resources/020-albums-resource.md", "link": "#custom-form-components", "text": "Custom Form Components", "status": "Anchor not found: #custom-form-components"}, {"file": "filament/resources/020-albums-resource.md", "link": "#advanced-table-features", "text": "Advanced Table Features", "status": "Anchor not found: #advanced-table-features"}, {"file": "filament/resources/020-albums-resource.md", "link": "#bulk-operations", "text": "Bulk Operations", "status": "Anchor not found: #bulk-operations"}, {"file": "filament/resources/020-albums-resource.md", "link": "#authorization", "text": "Authorization", "status": "Anchor not found: #authorization"}, {"file": "filament/resources/020-albums-resource.md", "link": "#resource-level-authorization", "text": "Resource-Level Authorization", "status": "Anchor not found: #resource-level-authorization"}, {"file": "filament/resources/020-albums-resource.md", "link": "#field-level-security", "text": "Field-Level Security", "status": "Anchor not found: #field-level-security"}, {"file": "filament/resources/020-albums-resource.md", "link": "#business-logic", "text": "Business Logic", "status": "Anchor not found: #business-logic"}, {"file": "filament/resources/020-albums-resource.md", "link": "#album-validation", "text": "Album Validation", "status": "Anchor not found: #album-validation"}, {"file": "filament/resources/020-albums-resource.md", "link": "#release-date-handling", "text": "Release Date Handling", "status": "Anchor not found: #release-date-handling"}, {"file": "filament/resources/020-albums-resource.md", "link": "#cover-art-management", "text": "Cover Art Management", "status": "Anchor not found: #cover-art-management"}, {"file": "filament/resources/020-albums-resource.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "filament/resources/020-albums-resource.md", "link": "#testing", "text": "Testing", "status": "Anchor not found: #testing"}, {"file": "filament/resources/040-categories-resource.md", "link": "#hierarchical-form-configuration", "text": "Hierarchical Form Configuration", "status": "Anchor not found: #hierarchical-form-configuration"}, {"file": "filament/resources/040-categories-resource.md", "link": "#tree-table-configuration", "text": "Tree Table Configuration", "status": "Anchor not found: #tree-table-configuration"}, {"file": "filament/resources/040-categories-resource.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "filament/resources/040-categories-resource.md", "link": "#polymorphic-relationships", "text": "Polymorphic Relationships", "status": "Anchor not found: #polymorphic-relationships"}, {"file": "filament/resources/040-categories-resource.md", "link": "#categorizable-implementation", "text": "Categorizable Implementation", "status": "Anchor not found: #categorizable-implementation"}, {"file": "filament/resources/040-categories-resource.md", "link": "#multi-model-assignment", "text": "Multi-Model Assignment", "status": "Anchor not found: #multi-model-assignment"}, {"file": "filament/resources/040-categories-resource.md", "link": "#category-types", "text": "Category Types", "status": "Anchor not found: #category-types"}, {"file": "filament/resources/040-categories-resource.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "filament/resources/040-categories-resource.md", "link": "#category-tree-visualization", "text": "Category Tree Visualization", "status": "Anchor not found: #category-tree-visualization"}, {"file": "filament/resources/040-categories-resource.md", "link": "#bulk-category-operations", "text": "Bulk Category Operations", "status": "Anchor not found: #bulk-category-operations"}, {"file": "filament/resources/040-categories-resource.md", "link": "#category-analytics", "text": "Category Analytics", "status": "Anchor not found: #category-analytics"}, {"file": "filament/resources/040-categories-resource.md", "link": "#business-logic", "text": "Business Logic", "status": "Anchor not found: #business-logic"}, {"file": "filament/resources/040-categories-resource.md", "link": "#category-validation", "text": "Category Validation", "status": "Anchor not found: #category-validation"}, {"file": "filament/resources/040-categories-resource.md", "link": "#hierarchy-constraints", "text": "Hierarchy Constraints", "status": "Anchor not found: #hierarchy-constraints"}, {"file": "filament/resources/040-categories-resource.md", "link": "#usage-tracking", "text": "Usage Tracking", "status": "Anchor not found: #usage-tracking"}, {"file": "filament/resources/040-categories-resource.md", "link": "#authorization", "text": "Authorization", "status": "Anchor not found: #authorization"}, {"file": "filament/resources/040-categories-resource.md", "link": "#testing", "text": "Testing", "status": "Anchor not found: #testing"}, {"file": "filament/resources/130-table-features.md", "link": "#sorting-and-grouping", "text": "Sorting and Grouping", "status": "Anchor not found: #sorting-and-grouping"}, {"file": "filament/resources/130-table-features.md", "link": "../performance/query-optimization.md", "text": "Performance Optimization", "status": "File not found: filament/performance/query-optimization.md"}, {"file": "filament/resources/130-table-features.md", "link": "../ui/table-components.md", "text": "UI Components", "status": "File not found: filament/ui/table-components.md"}, {"file": "filament/resources/050-playlists-resource.md", "link": "130-form-components.md", "text": "Form Components", "status": "File not found: filament/resources/130-form-components.md"}, {"file": "filament/resources/090-invoice-lines-resource.md", "link": "../business/pricing-models.md", "text": "Pricing Strategies", "status": "File not found: filament/business/pricing-models.md"}, {"file": "filament/resources/090-invoice-lines-resource.md", "link": "../analytics/sales-reporting.md", "text": "Sales Analytics", "status": "File not found: filament/analytics/sales-reporting.md"}, {"file": "filament/resources/100-employees-resource.md", "link": "../security/role-management.md", "text": "Role Management", "status": "File not found: filament/security/role-management.md"}, {"file": "filament/resources/100-employees-resource.md", "link": "../analytics/hr-metrics.md", "text": "HR Analytics", "status": "File not found: filament/analytics/hr-metrics.md"}, {"file": "filament/resources/110-users-resource.md", "link": "../security/authentication.md", "text": "Authentication Systems", "status": "File not found: filament/security/authentication.md"}, {"file": "filament/resources/110-users-resource.md", "link": "../analytics/user-metrics.md", "text": "User Analytics", "status": "File not found: filament/analytics/user-metrics.md"}, {"file": "filament/resources/000-index.md", "link": "#customer-management", "text": "Customer Management", "status": "Anchor not found: #customer-management"}, {"file": "filament/resources/000-index.md", "link": "#sales--invoicing", "text": "Sales & Invoicing", "status": "Anchor not found: #sales--invoicing"}, {"file": "filament/resources/060-media-types-resource.md", "link": "../forms/file-uploads.md", "text": "File Upload Handling", "status": "File not found: filament/forms/file-uploads.md"}, {"file": "filament/resources/060-media-types-resource.md", "link": "../forms/validation.md", "text": "Validation Strategies", "status": "File not found: filament/forms/validation.md"}, {"file": "filament/diagrams/020-database-schema.md", "link": "#wcag-21-aa-compliance", "text": "WCAG 2.1 AA Compliance", "status": "Anchor not found: #wcag-21-aa-compliance"}, {"file": "filament/diagrams/020-database-schema.md", "link": "030-relationship-mapping.md", "text": "Relationship Mapping", "status": "File not found: filament/diagrams/030-relationship-mapping.md"}, {"file": "filament/diagrams/060-filament-panel-architecture.md", "link": "070-authentication-flow.md", "text": "Authentication Flow", "status": "File not found: filament/diagrams/070-authentication-flow.md"}, {"file": "filament/diagrams/060-filament-panel-architecture.md", "link": "090-navigation-structure.md", "text": "Navigation Structure", "status": "File not found: filament/diagrams/090-navigation-structure.md"}, {"file": "filament/diagrams/060-filament-panel-architecture.md", "link": "100-user-journey-maps.md", "text": "User Journey Maps", "status": "File not found: filament/diagrams/100-user-journey-maps.md"}, {"file": "filament/diagrams/README.md", "link": "030-relationship-mapping.md", "text": "Relationship Mapping", "status": "File not found: filament/diagrams/030-relationship-mapping.md"}, {"file": "filament/diagrams/README.md", "link": "040-indexing-strategy.md", "text": "Indexing Strategy", "status": "File not found: filament/diagrams/040-indexing-strategy.md"}, {"file": "filament/diagrams/README.md", "link": "070-authentication-flow.md", "text": "Authentication Flow", "status": "File not found: filament/diagrams/070-authentication-flow.md"}, {"file": "filament/diagrams/README.md", "link": "080-data-flow-diagrams.md", "text": "Data Flow Diagrams", "status": "File not found: filament/diagrams/080-data-flow-diagrams.md"}, {"file": "filament/diagrams/README.md", "link": "090-navigation-structure.md", "text": "Navigation Structure", "status": "File not found: filament/diagrams/090-navigation-structure.md"}, {"file": "filament/diagrams/README.md", "link": "100-user-journey-maps.md", "text": "User Journey Maps", "status": "File not found: filament/diagrams/100-user-journey-maps.md"}, {"file": "filament/diagrams/README.md", "link": "110-wireframes.md", "text": "Wireframes", "status": "File not found: filament/diagrams/110-wireframes.md"}, {"file": "filament/diagrams/README.md", "link": "120-accessibility-features.md", "text": "Accessibility Features", "status": "File not found: filament/diagrams/120-accessibility-features.md"}, {"file": "filament/diagrams/010-entity-relationship-diagrams.md", "link": "030-relationship-mapping.md", "text": "Relationship Mapping", "status": "File not found: filament/diagrams/030-relationship-mapping.md"}, {"file": "filament/diagrams/050-system-architecture.md", "link": "070-authentication-flow.md", "text": "Authentication Flow", "status": "File not found: filament/diagrams/070-authentication-flow.md"}, {"file": "filament/diagrams/050-system-architecture.md", "link": "080-data-flow-diagrams.md", "text": "Data Flow Diagrams", "status": "File not found: filament/diagrams/080-data-flow-diagrams.md"}, {"file": "filament/diagrams/000-index.md", "link": "#wcag-21-aa-compliance", "text": "WCAG 2.1 AA Compliance", "status": "Anchor not found: #wcag-21-aa-compliance"}, {"file": "filament/diagrams/000-index.md", "link": "030-relationship-mapping.md", "text": "Relationship Mapping", "status": "File not found: filament/diagrams/030-relationship-mapping.md"}, {"file": "filament/diagrams/000-index.md", "link": "040-indexing-strategy.md", "text": "Indexing Strategy", "status": "File not found: filament/diagrams/040-indexing-strategy.md"}, {"file": "filament/diagrams/000-index.md", "link": "070-authentication-flow.md", "text": "Authentication Flow", "status": "File not found: filament/diagrams/070-authentication-flow.md"}, {"file": "filament/diagrams/000-index.md", "link": "080-data-flow-diagrams.md", "text": "Data Flow Diagrams", "status": "File not found: filament/diagrams/080-data-flow-diagrams.md"}, {"file": "filament/diagrams/000-index.md", "link": "090-user-workflows.md", "text": "User Workflows", "status": "File not found: filament/diagrams/090-user-workflows.md"}, {"file": "filament/diagrams/000-index.md", "link": "100-admin-workflows.md", "text": "Admin Workflows", "status": "File not found: filament/diagrams/100-admin-workflows.md"}, {"file": "filament/diagrams/000-index.md", "link": "110-api-workflows.md", "text": "API Workflows", "status": "File not found: filament/diagrams/110-api-workflows.md"}, {"file": "filament/diagrams/000-index.md", "link": "120-error-handling.md", "text": "Erro<PERSON>", "status": "File not found: filament/diagrams/120-error-handling.md"}, {"file": "filament/features/README.md", "link": "050-custom-pages.md", "text": "Custom Pages", "status": "File not found: filament/features/050-custom-pages.md"}, {"file": "filament/features/README.md", "link": "060-sales-analytics.md", "text": "Sales Analytics", "status": "File not found: filament/features/060-sales-analytics.md"}, {"file": "filament/features/README.md", "link": "070-employee-hierarchy.md", "text": "Employee Hierarchy", "status": "File not found: filament/features/070-employee-hierarchy.md"}, {"file": "filament/features/README.md", "link": "080-music-discovery.md", "text": "Music Discovery", "status": "File not found: filament/features/080-music-discovery.md"}, {"file": "filament/features/README.md", "link": "100-import-export.md", "text": "Import Export", "status": "File not found: filament/features/100-import-export.md"}, {"file": "filament/features/README.md", "link": "110-bulk-operations.md", "text": "Bulk Operations", "status": "File not found: filament/features/110-bulk-operations.md"}, {"file": "filament/features/README.md", "link": "120-custom-actions.md", "text": "Custom Actions", "status": "File not found: filament/features/120-custom-actions.md"}, {"file": "filament/features/README.md", "link": "130-advanced-forms.md", "text": "Advanced Forms", "status": "File not found: filament/features/130-advanced-forms.md"}, {"file": "filament/features/README.md", "link": "140-table-features.md", "text": "Table Features", "status": "File not found: filament/features/140-table-features.md"}, {"file": "filament/features/README.md", "link": "150-relationship-management.md", "text": "Relationship Management", "status": "File not found: filament/features/150-relationship-management.md"}, {"file": "filament/features/README.md", "link": "160-media-library.md", "text": "Media Library", "status": "File not found: filament/features/160-media-library.md"}, {"file": "filament/features/README.md", "link": "170-activity-logging.md", "text": "Activity Logging", "status": "File not found: filament/features/170-activity-logging.md"}, {"file": "filament/features/README.md", "link": "180-notification-system.md", "text": "Notification System", "status": "File not found: filament/features/180-notification-system.md"}, {"file": "filament/features/000-index.md", "link": "#widget-system", "text": "Widget System", "status": "Anchor not found: #widget-system"}, {"file": "filament/features/000-index.md", "link": "#search--navigation", "text": "Search & Navigation", "status": "Anchor not found: #search--navigation"}, {"file": "filament/features/000-index.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "filament/setup/040-navigation-configuration.md", "link": "#navigation-groups-1", "text": "Navigation Groups", "status": "Anchor not found: #navigation-groups-1"}, {"file": "filament/setup/040-navigation-configuration.md", "link": "#analytics--reports", "text": "Analytics & Reports", "status": "Anchor not found: #analytics--reports"}, {"file": "filament/setup/010-panel-configuration.md", "link": "#panel-configuration", "text": "Panel Configuration", "status": "Anchor not found: #panel-configuration"}, {"file": "filament/setup/010-panel-configuration.md", "link": "#middleware-setup", "text": "Middleware Setup", "status": "Anchor not found: #middleware-setup"}, {"file": "filament/setup/010-panel-configuration.md", "link": "#navigation-configuration", "text": "Navigation Configuration", "status": "Anchor not found: #navigation-configuration"}, {"file": "filament/setup/010-panel-configuration.md", "link": "#theme-and-styling", "text": "Theme and Styling", "status": "Anchor not found: #theme-and-styling"}, {"file": "filament/setup/010-panel-configuration.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "filament/setup/010-panel-configuration.md", "link": "#environment-specific-settings", "text": "Environment-Specific Settings", "status": "Anchor not found: #environment-specific-settings"}, {"file": "filament/setup/070-sqlite-optimization.md", "link": "../deployment/040-monitoring.md", "text": "Monitoring", "status": "File not found: filament/deployment/040-monitoring.md"}, {"file": "filament/setup/020-authentication-setup.md", "link": "#custom-authentication-pages", "text": "Custom Authentication Pages", "status": "Anchor not found: #custom-authentication-pages"}, {"file": "filament/setup/000-index.md", "link": "#authentication--security", "text": "Authentication & Security", "status": "Anchor not found: #authentication--security"}, {"file": "filament/setup/000-index.md", "link": "#configuration--optimization", "text": "Configuration & Optimization", "status": "Anchor not found: #configuration--optimization"}, {"file": "filament/models/110-model-policies.md", "link": "../../security/rbac-implementation.md", "text": "RBAC Implementation", "status": "File not found: security/rbac-implementation.md"}, {"file": "filament/models/110-model-policies.md", "link": "../../security/security-guidelines.md", "text": "Security Best Practices", "status": "File not found: security/security-guidelines.md"}, {"file": "filament/models/040-relationship-patterns.md", "link": "../040-validation-rules.md", "text": "Validation Rules Guide", "status": "File not found: filament/040-validation-rules.md"}, {"file": "filament/models/100-model-observers.md", "link": "../../architecture/event-driven-patterns.md", "text": "Event-Driven Architecture", "status": "File not found: architecture/event-driven-patterns.md"}, {"file": "filament/models/100-model-observers.md", "link": "../../performance/optimization-strategies.md", "text": "Performance Optimization", "status": "File not found: performance/optimization-strategies.md"}, {"file": "filament/models/070-user-stamps.md", "link": "../040-validation-rules.md", "text": "Validation Rules Guide", "status": "File not found: filament/040-validation-rules.md"}, {"file": "filament/models/000-index.md", "link": "060-categorizable-trait.md", "text": "Categorizable Trait", "status": "File not found: filament/models/060-categorizable-trait.md"}, {"file": "filament/models/000-index.md", "link": "080-secondary-keys.md", "text": "Secondary Keys", "status": "File not found: filament/models/080-secondary-keys.md"}, {"file": "filament/models/000-index.md", "link": "090-category-management.md", "text": "Category Management", "status": "File not found: filament/models/090-category-management.md"}, {"file": "filament/models/000-index.md", "link": "100-tree-operations.md", "text": "Tree Operations", "status": "File not found: filament/models/100-tree-operations.md"}, {"file": "filament/models/000-index.md", "link": "110-performance-optimization.md", "text": "Performance Optimization", "status": "File not found: filament/models/110-performance-optimization.md"}, {"file": "filament/models/000-index.md", "link": "120-scopes-filters.md", "text": "Scopes and Filters", "status": "File not found: filament/models/120-scopes-filters.md"}, {"file": "filament/models/000-index.md", "link": "130-accessors-mutators.md", "text": "Accessors and Mutators", "status": "File not found: filament/models/130-accessors-mutators.md"}, {"file": "filament/models/000-index.md", "link": "140-model-events.md", "text": "Model Events", "status": "File not found: filament/models/140-model-events.md"}, {"file": "filament/models/000-index.md", "link": "150-custom-methods.md", "text": "Custom Methods", "status": "File not found: filament/models/150-custom-methods.md"}, {"file": "filament/models/120-model-scopes.md", "link": "../040-validation-rules.md", "text": "Validation Rules Guide", "status": "File not found: filament/040-validation-rules.md"}, {"file": "filament/models/120-model-scopes.md", "link": "../../performance/query-optimization.md", "text": "Performance Optimization", "status": "File not found: performance/query-optimization.md"}, {"file": "filament/testing/070-table-testing.md", "link": "#column-configuration-testing", "text": "Column Configuration Testing", "status": "Anchor not found: #column-configuration-testing"}, {"file": "filament/testing/070-table-testing.md", "link": "#bulk-operations-testing", "text": "Bulk Operations Testing", "status": "Anchor not found: #bulk-operations-testing"}, {"file": "filament/testing/070-table-testing.md", "link": "#accessibility-testing", "text": "Accessibility Testing", "status": "Anchor not found: #accessibility-testing"}, {"file": "filament/testing/150-accessibility-testing.md", "link": "#wcag-21-aa-compliance-testing", "text": "WCAG 2.1 AA Compliance Testing", "status": "Anchor not found: #wcag-21-aa-compliance-testing"}, {"file": "filament/testing/040-ci-integration.md", "link": "120-performance-testing.md", "text": "Performance Testing", "status": "File not found: filament/testing/120-performance-testing.md"}, {"file": "filament/testing/040-ci-integration.md", "link": "150-security-testing.md", "text": "Security Testing", "status": "File not found: filament/testing/150-security-testing.md"}, {"file": "filament/testing/040-ci-integration.md", "link": "040-resource-testing.md", "text": "Resource Testing", "status": "File not found: filament/testing/040-resource-testing.md"}, {"file": "filament/testing/030-test-data-management.md", "link": "040-resource-testing.md", "text": "Resource Testing", "status": "File not found: filament/testing/040-resource-testing.md"}, {"file": "filament/testing/030-test-data-management.md", "link": "120-performance-testing.md", "text": "Performance Testing", "status": "File not found: filament/testing/120-performance-testing.md"}, {"file": "filament/testing/030-test-data-management.md", "link": "110-database-testing.md", "text": "Database Testing", "status": "File not found: filament/testing/110-database-testing.md"}, {"file": "filament/testing/020-test-environment-setup.md", "link": "040-resource-testing.md", "text": "Resource Testing", "status": "File not found: filament/testing/040-resource-testing.md"}, {"file": "filament/testing/020-test-environment-setup.md", "link": "120-performance-testing.md", "text": "Performance Testing", "status": "File not found: filament/testing/120-performance-testing.md"}, {"file": "filament/testing/050-resource-testing.md", "link": "#authorization-testing", "text": "Authorization Testing", "status": "Anchor not found: #authorization-testing"}, {"file": "filament/testing/050-resource-testing.md", "link": "#validation-testing", "text": "Validation Testing", "status": "Anchor not found: #validation-testing"}, {"file": "filament/testing/050-resource-testing.md", "link": "#relationship-testing", "text": "Relationship Testing", "status": "Anchor not found: #relationship-testing"}, {"file": "filament/testing/050-resource-testing.md", "link": "#advanced-feature-testing", "text": "Advanced Feature Testing", "status": "Anchor not found: #advanced-feature-testing"}, {"file": "filament/testing/050-resource-testing.md", "link": "#performance-testing", "text": "Performance Testing", "status": "Anchor not found: #performance-testing"}, {"file": "filament/testing/060-form-testing.md", "link": "../../../testing/020-unit-testing-guide.md", "text": "Validation Testing", "status": "Path outside base directory: .ai/guides/chinook/filament/testing/../../../testing/020-unit-testing-guide.md"}, {"file": "filament/testing/110-api-testing.md", "link": "#error-handling-testing", "text": "Error Handling Testing", "status": "Anchor not found: #error-handling-testing"}, {"file": "filament/testing/110-api-testing.md", "link": "#integration-testing", "text": "Integration Testing", "status": "Anchor not found: #integration-testing"}, {"file": "filament/testing/010-testing-strategy.md", "link": "040-resource-testing.md", "text": "Resource Testing", "status": "File not found: filament/testing/040-resource-testing.md"}, {"file": "filament/testing/010-testing-strategy.md", "link": "150-performance-testing.md", "text": "Performance Testing", "status": "File not found: filament/testing/150-performance-testing.md"}, {"file": "filament/testing/010-testing-strategy.md", "link": "180-browser-testing.md", "text": "Browser Testing", "status": "File not found: filament/testing/180-browser-testing.md"}, {"file": "filament/deployment/040-ssl-configuration.md", "link": "#lets-encrypt-setup", "text": "Let's Encrypt Setup", "status": "Anchor not found: #lets-encrypt-setup"}, {"file": "filament/deployment/030-security-hardening.md", "link": "#csrf-protection", "text": "CSRF Protection", "status": "Anchor not found: #csrf-protection"}, {"file": "filament/deployment/030-security-hardening.md", "link": "#input-validation", "text": "Input Validation", "status": "Anchor not found: #input-validation"}, {"file": "filament/deployment/030-security-hardening.md", "link": "#monitoring-and-logging", "text": "Monitoring and Logging", "status": "Anchor not found: #monitoring-and-logging"}, {"file": "filament/deployment/030-security-hardening.md", "link": "#security-auditing", "text": "Security Auditing", "status": "Anchor not found: #security-auditing"}, {"file": "filament/deployment/150-performance-optimization-guide.md", "link": "#monitoring--profiling", "text": "Monitoring & Profiling", "status": "Anchor not found: #monitoring--profiling"}, {"file": "filament/deployment/150-performance-optimization-guide.md", "link": "140-browser-testing.md", "text": "Browser Testing Guide", "status": "File not found: filament/deployment/140-browser-testing.md"}, {"file": "filament/deployment/150-performance-optimization-guide.md", "link": "160-scaling-strategies.md", "text": "Scaling Strategies Guide", "status": "File not found: filament/deployment/160-scaling-strategies.md"}, {"file": "filament/deployment/README.md", "link": "090-monitoring-setup.md", "text": "Monitoring Setup", "status": "File not found: filament/deployment/090-monitoring-setup.md"}, {"file": "filament/deployment/README.md", "link": "100-logging-configuration.md", "text": "Logging Configuration", "status": "File not found: filament/deployment/100-logging-configuration.md"}, {"file": "filament/deployment/README.md", "link": "110-backup-strategy.md", "text": "Backup Strategy", "status": "File not found: filament/deployment/110-backup-strategy.md"}, {"file": "filament/deployment/README.md", "link": "120-maintenance-procedures.md", "text": "Maintenance Procedures", "status": "File not found: filament/deployment/120-maintenance-procedures.md"}, {"file": "filament/deployment/README.md", "link": "130-cicd-pipeline.md", "text": "CI/CD Pipeline", "status": "File not found: filament/deployment/130-cicd-pipeline.md"}, {"file": "filament/deployment/README.md", "link": "140-docker-deployment.md", "text": "Docker Deployment", "status": "File not found: filament/deployment/140-docker-deployment.md"}, {"file": "filament/deployment/README.md", "link": "150-cloud-deployment.md", "text": "Cloud Deployment", "status": "File not found: filament/deployment/150-cloud-deployment.md"}, {"file": "filament/deployment/README.md", "link": "160-scaling-strategies.md", "text": "Scaling Strategies", "status": "File not found: filament/deployment/160-scaling-strategies.md"}, {"file": "filament/deployment/060-database-optimization.md", "link": "#backup--recovery", "text": "Backup & Recovery", "status": "Anchor not found: #backup--recovery"}, {"file": "filament/deployment/060-database-optimization.md", "link": "#monitoring--maintenance", "text": "Monitoring & Maintenance", "status": "Anchor not found: #monitoring--maintenance"}, {"file": "filament/deployment/050-performance-optimization.md", "link": "090-monitoring-setup.md", "text": "Monitoring Setup", "status": "File not found: filament/deployment/090-monitoring-setup.md"}, {"file": "filament/deployment/020-server-configuration.md", "link": "#ssl-tls-configuration", "text": "SSL/TLS Configuration", "status": "Anchor not found: #ssl-tls-configuration"}, {"file": "filament/deployment/020-server-configuration.md", "link": "090-monitoring-setup.md", "text": "Monitoring Setup", "status": "File not found: filament/deployment/090-monitoring-setup.md"}, {"file": "filament/deployment/010-production-environment.md", "link": "090-monitoring-setup.md", "text": "Monitoring Setup", "status": "File not found: filament/deployment/090-monitoring-setup.md"}, {"file": "filament/deployment/000-index.md", "link": "#monitoring--maintenance", "text": "Monitoring & Maintenance", "status": "Anchor not found: #monitoring--maintenance"}, {"file": "filament/deployment/000-index.md", "link": "090-monitoring-setup.md", "text": "Monitoring Setup", "status": "File not found: filament/deployment/090-monitoring-setup.md"}, {"file": "filament/deployment/000-index.md", "link": "100-logging-configuration.md", "text": "Logging Configuration", "status": "File not found: filament/deployment/100-logging-configuration.md"}, {"file": "filament/deployment/000-index.md", "link": "110-backup-strategy.md", "text": "Backup Strategy", "status": "File not found: filament/deployment/110-backup-strategy.md"}, {"file": "filament/deployment/000-index.md", "link": "120-maintenance-procedures.md", "text": "Maintenance Procedures", "status": "File not found: filament/deployment/120-maintenance-procedures.md"}, {"file": "filament/deployment/000-index.md", "link": "130-cicd-pipeline.md", "text": "CI/CD Pipeline", "status": "File not found: filament/deployment/130-cicd-pipeline.md"}, {"file": "filament/deployment/000-index.md", "link": "140-docker-deployment.md", "text": "Docker Deployment", "status": "File not found: filament/deployment/140-docker-deployment.md"}, {"file": "filament/deployment/000-index.md", "link": "150-cloud-deployment.md", "text": "Cloud Deployment", "status": "File not found: filament/deployment/150-cloud-deployment.md"}, {"file": "filament/deployment/000-index.md", "link": "160-scaling-strategies.md", "text": "Scaling Strategies", "status": "File not found: filament/deployment/160-scaling-strategies.md"}, {"file": "filament/deployment/080-caching-strategy.md", "link": "#session--user-caching", "text": "Session & User Caching", "status": "Anchor not found: #session--user-caching"}, {"file": "filament/deployment/080-caching-strategy.md", "link": "#file--asset-caching", "text": "File & Asset Caching", "status": "Anchor not found: #file--asset-caching"}, {"file": "filament/deployment/080-caching-strategy.md", "link": "#monitoring--optimization", "text": "Monitoring & Optimization", "status": "Anchor not found: #monitoring--optimization"}, {"file": "filament/deployment/080-caching-strategy.md", "link": "090-monitoring-setup.md", "text": "Monitoring Setup Guide", "status": "File not found: filament/deployment/090-monitoring-setup.md"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#12-configuration-publishing", "text": "1.2. Configuration Publishing", "status": "Anchor not found: #12-configuration-publishing"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#13-environment-configuration", "text": "1.3. Environment Configuration", "status": "Anchor not found: #13-environment-configuration"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#authorization--security", "text": "Authorization & Security", "status": "Anchor not found: #authorization--security"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#21-gate-based-authorization", "text": "2.1. Gate-Based Authorization", "status": "Anchor not found: #21-gate-based-authorization"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#22-environment-specific-access", "text": "2.2. Environment-Specific Access", "status": "Anchor not found: #22-environment-specific-access"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#23-ip-whitelisting", "text": "2.3. IP Whitelisting", "status": "Anchor not found: #23-ip-whitelisting"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#31-watcher-configuration", "text": "3.1. Watcher Configuration", "status": "Anchor not found: #31-watcher-configuration"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#32-filtering--sampling", "text": "3.2. Fi<PERSON><PERSON> & Sam<PERSON>", "status": "Anchor not found: #32-filtering--sampling"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#33-performance-impact-mitigation", "text": "3.3. Performance Impact Mitigation", "status": "Anchor not found: #33-performance-impact-mitigation"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#data-pruning--storage-management", "text": "Data Pruning & Storage Management", "status": "Anchor not found: #data-pruning--storage-management"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#41-automated-pruning", "text": "4.1. Automated Pruning", "status": "Anchor not found: #41-automated-pruning"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#42-storage-optimization", "text": "4.2. Storage Optimization", "status": "Anchor not found: #42-storage-optimization"}, {"file": "packages/030-laravel-telescope-guide.md", "link": "#43-custom-retention-policies", "text": "4.3. Custom Retention Policies", "status": "Anchor not found: #43-custom-retention-policies"}, {"file": "packages/130-spatie-laravel-settings-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/130-spatie-laravel-settings-guide.md", "link": "120-nn<PERSON><PERSON>-world-guide.md", "text": "NNJeim World Guide", "status": "File not found: packages/120-nnjeim-world-guide.md"}, {"file": "packages/100-spatie-media-library-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/100-spatie-media-library-guide.md", "link": "#file-conversions--processing", "text": "File Conversions & Processing", "status": "Anchor not found: #file-conversions--processing"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#12-configuration-publishing", "text": "1.2. Configuration Publishing", "status": "Anchor not found: #12-configuration-publishing"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#13-environment-setup", "text": "1.3. Environment Setup", "status": "Anchor not found: #13-environment-setup"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#21-basic-dashboard-setup", "text": "2.1. Basic Dashboard Setup", "status": "Anchor not found: #21-basic-dashboard-setup"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#22-authentication--authorization", "text": "2.2. Authentication & Authorization", "status": "Anchor not found: #22-authentication--authorization"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#23-custom-dashboard-views", "text": "2.3. Custom Dashboard Views", "status": "Anchor not found: #23-custom-dashboard-views"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#31-queue-worker-settings", "text": "3.1. <PERSON><PERSON> Worker Settings", "status": "Anchor not found: #31-queue-worker-settings"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#32-supervisor-configuration", "text": "3.2. Supervisor Configuration", "status": "Anchor not found: #32-supervisor-configuration"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#33-auto-scaling-setup", "text": "3.3. Auto-Scaling Setup", "status": "Anchor not found: #33-auto-scaling-setup"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#41-horizon-watcher-integration", "text": "4.1. Horizon Watcher Integration", "status": "Anchor not found: #41-horizon-watcher-integration"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#42-custom-metrics-collection", "text": "4.2. Custom Metrics Collection", "status": "Anchor not found: #42-custom-metrics-collection"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#43-alert-configuration", "text": "4.3. <PERSON><PERSON>gu<PERSON>", "status": "Anchor not found: #43-alert-configuration"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#performance-tuning", "text": "Performance Tuning", "status": "Anchor not found: #performance-tuning"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#integration-strategies", "text": "Integration Strategies", "status": "Anchor not found: #integration-strategies"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#best-practices", "text": "Best Practices", "status": "Anchor not found: #best-practices"}, {"file": "packages/050-laravel-horizon-guide.md", "link": "#troubleshooting", "text": "Troubleshooting", "status": "Anchor not found: #troubleshooting"}, {"file": "packages/140-laravel-optimize-database-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/140-laravel-optimize-database-guide.md", "link": "#query-performance-monitoring", "text": "Query Performance Monitoring", "status": "Anchor not found: #query-performance-monitoring"}, {"file": "packages/140-laravel-optimize-database-guide.md", "link": "#sqlite-specific-optimizations", "text": "SQLite-Specific Optimizations", "status": "Anchor not found: #sqlite-specific-optimizations"}, {"file": "packages/140-laravel-optimize-database-guide.md", "link": "#performance-testing", "text": "Performance Testing", "status": "Anchor not found: #performance-testing"}, {"file": "packages/140-laravel-optimize-database-guide.md", "link": "#production-monitoring", "text": "Production Monitoring", "status": "Anchor not found: #production-monitoring"}, {"file": "packages/140-laravel-optimize-database-guide.md", "link": "#troubleshooting", "text": "Troubleshooting", "status": "Anchor not found: #troubleshooting"}, {"file": "packages/140-laravel-optimize-database-guide.md", "link": "#best-practices", "text": "Best Practices", "status": "Anchor not found: #best-practices"}, {"file": "packages/140-laravel-optimize-database-guide.md", "link": "../testing/010-pest-testing-guide.md", "text": "Modern Testing with Pest Guide", "status": "File not found: testing/010-pest-testing-guide.md"}, {"file": "packages/090-laravel-workos-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/090-laravel-workos-guide.md", "link": "#monitoring--troubleshooting", "text": "Monitoring & Troubleshooting", "status": "Anchor not found: #monitoring--troubleshooting"}, {"file": "packages/000-packages-index.md", "link": "#backup--monitoring", "text": "Backup & Monitoring", "status": "Anchor not found: #backup--monitoring"}, {"file": "packages/000-packages-index.md", "link": "#performance--optimization", "text": "Performance & Optimization", "status": "Anchor not found: #performance--optimization"}, {"file": "packages/000-packages-index.md", "link": "#1-<PERSON><PERSON><PERSON>-backup", "text": "1. <PERSON><PERSON>", "status": "Anchor not found: #1-la<PERSON>l-backup"}, {"file": "packages/000-packages-index.md", "link": "#2-laravel-pulse", "text": "2. <PERSON><PERSON>", "status": "Anchor not found: #2-laravel-pulse"}, {"file": "packages/000-packages-index.md", "link": "#3-laravel-telescope", "text": "3. <PERSON><PERSON> Teles<PERSON>", "status": "Anchor not found: #3-la<PERSON>l-telescope"}, {"file": "packages/000-packages-index.md", "link": "#4-laravel-octane-with-frankenphp", "text": "4. <PERSON><PERSON> with FrankenPHP", "status": "Anchor not found: #4-laravel-octane-with-frankenphp"}, {"file": "packages/000-packages-index.md", "link": "#5-laravel-horizon", "text": "5. <PERSON><PERSON>", "status": "Anchor not found: #5-laravel-horizon"}, {"file": "packages/000-packages-index.md", "link": "#6-laravel-data", "text": "6. <PERSON><PERSON>", "status": "Anchor not found: #6-laravel-data"}, {"file": "packages/000-packages-index.md", "link": "#7-laravel-fractal", "text": "7. <PERSON><PERSON>", "status": "Anchor not found: #7-laravel-fractal"}, {"file": "packages/000-packages-index.md", "link": "#8-laravel-sanctum", "text": "8. <PERSON><PERSON>", "status": "Anchor not found: #8-laravel-sanctum"}, {"file": "packages/000-packages-index.md", "link": "#9-<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "text": "9. <PERSON><PERSON>", "status": "Anchor not found: #9-<PERSON><PERSON><PERSON>-<PERSON><PERSON>"}, {"file": "packages/000-packages-index.md", "link": "#10-laravel-query-builder", "text": "10. <PERSON><PERSON> Query Builder", "status": "Anchor not found: #10-laravel-query-builder"}, {"file": "packages/000-packages-index.md", "link": "#11-spatie-comments", "text": "11. <PERSON><PERSON>", "status": "Anchor not found: #11-spatie-comments"}, {"file": "packages/000-packages-index.md", "link": "#12-laravel-folio", "text": "12. <PERSON><PERSON>", "status": "Anchor not found: #12-laravel-folio"}, {"file": "packages/000-packages-index.md", "link": "#13-nn<PERSON><PERSON>-world", "text": "13. NNJeim World", "status": "Anchor not found: #13-nn<PERSON><PERSON>-world"}, {"file": "packages/000-packages-index.md", "link": "#14-laravel-database-optimization", "text": "14. Laravel Database Optimization", "status": "Anchor not found: #14-laravel-database-optimization"}, {"file": "packages/000-packages-index.md", "link": "#15-enhanced-spatie-activitylog", "text": "15. Enhanced Spatie ActivityLog", "status": "Anchor not found: #15-enhanced-spatie-activitylog"}, {"file": "packages/000-packages-index.md", "link": "140-laravel-database-optimization-guide.md", "text": "Laravel Database Optimization Guide", "status": "File not found: packages/140-laravel-database-optimization-guide.md"}, {"file": "packages/000-packages-index.md", "link": "150-enhanced-spatie-activitylog-guide.md", "text": "Enhanced Spatie ActivityLog Guide", "status": "File not found: packages/150-enhanced-spatie-activitylog-guide.md"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#12-configuration-publishing", "text": "1.2. Configuration Publishing", "status": "Anchor not found: #12-configuration-publishing"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#13-environment-configuration", "text": "1.3. Environment Configuration", "status": "Anchor not found: #13-environment-configuration"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#21-local-storage-setup", "text": "2.1. Local Storage Setup", "status": "Anchor not found: #21-local-storage-setup"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#22-aws-s3-configuration", "text": "2.2. AWS S3 Configuration", "status": "Anchor not found: #22-aws-s3-configuration"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#23-google-cloud-storage", "text": "2.3. Google Cloud Storage", "status": "Anchor not found: #23-google-cloud-storage"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#24-multi-destination-strategy", "text": "2.4. Multi-Destination Strategy", "status": "Anchor not found: #24-multi-destination-strategy"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#31-basic-backup-settings", "text": "3.1. Basic Backup Settings", "status": "Anchor not found: #31-basic-backup-settings"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#32-database-backup-options", "text": "3.2. Database Backup Options", "status": "Anchor not found: #32-database-backup-options"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#33-file-backup-configuration", "text": "3.3. File Backup Configuration", "status": "Anchor not found: #33-file-backup-configuration"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#34-exclusion-patterns", "text": "3.4. <PERSON><PERSON><PERSON><PERSON>", "status": "Anchor not found: #34-exclusion-patterns"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#41-laravel-task-scheduler", "text": "4.1. <PERSON><PERSON> Scheduler", "status": "Anchor not found: #41-laravel-task-scheduler"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#42-cron-configuration", "text": "4.2. <PERSON>ron Configuration", "status": "Anchor not found: #42-cron-configuration"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#43-backup-frequency-strategies", "text": "4.3. <PERSON><PERSON> Frequency Strategies", "status": "Anchor not found: #43-backup-frequency-strategies"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#51-email-notifications", "text": "5.1. Email Notifications", "status": "Anchor not found: #51-email-notifications"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#52-slack-integration", "text": "5.2. <PERSON><PERSON><PERSON>", "status": "Anchor not found: #52-slack-integration"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#53-custom-notification-channels", "text": "5.3. Custom Notification Channels", "status": "Anchor not found: #53-custom-notification-channels"}, {"file": "packages/010-laravel-backup-guide.md", "link": "#monitoring--health-checks", "text": "Monitoring & Health Checks", "status": "Anchor not found: #monitoring--health-checks"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#12-frankenphp-server-setup", "text": "1.2. FrankenPHP Server Setup", "status": "Anchor not found: #12-frankenphp-server-setup"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#13-environment-configuration", "text": "1.3. Environment Configuration", "status": "Anchor not found: #13-environment-configuration"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#21-basic-server-settings", "text": "2.1. Basic Server Settings", "status": "Anchor not found: #21-basic-server-settings"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#22-performance-optimization", "text": "2.2. Performance Optimization", "status": "Anchor not found: #22-performance-optimization"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#23-ssltls-configuration", "text": "2.3. SSL/TLS Configuration", "status": "Anchor not found: #23-ssltls-configuration"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#31-memory-leak-prevention", "text": "3.1. Memory Leak Prevention", "status": "Anchor not found: #31-memory-leak-prevention"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#32-resource-optimization", "text": "3.2. Resource Optimization", "status": "Anchor not found: #32-resource-optimization"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#33-garbage-collection-tuning", "text": "3.3. Garbage Collection Tuning", "status": "Anchor not found: #33-garbage-collection-tuning"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#41-docker-configuration", "text": "4.1. <PERSON><PERSON> Configuration", "status": "Anchor not found: #41-docker-configuration"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#42-load-balancing", "text": "4.2. <PERSON><PERSON>", "status": "Anchor not found: #42-load-balancing"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#43-scaling-strategies", "text": "4.3. Scaling Strategies", "status": "Anchor not found: #43-scaling-strategies"}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "link": "#monitoring--troubleshooting", "text": "Monitoring & Troubleshooting", "status": "Anchor not found: #monitoring--troubleshooting"}, {"file": "packages/120-spatie-activitylog-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/120-spatie-activitylog-guide.md", "link": "#security--compliance", "text": "Security & Compliance", "status": "Anchor not found: #security--compliance"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#12-configuration-publishing", "text": "1.2. Configuration Publishing", "status": "Anchor not found: #12-configuration-publishing"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#13-basic-setup", "text": "1.3. Basic Setup", "status": "Anchor not found: #13-basic-setup"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#21-basic-transformers", "text": "2.1. Basic Transformers", "status": "Anchor not found: #21-basic-transformers"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#22-advanced-transformers", "text": "2.2. Advanced Transformers", "status": "Anchor not found: #22-advanced-transformers"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#23-nested-transformers", "text": "2.3. Nested Transformers", "status": "Anchor not found: #23-nested-transformers"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#31-item-resources", "text": "3.1. Item Resources", "status": "Anchor not found: #31-item-resources"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#32-collection-resources", "text": "3.2. Collection Resources", "status": "Anchor not found: #32-collection-resources"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#33-relationship-handling", "text": "3.3. Relationship Handling", "status": "Anchor not found: #33-relationship-handling"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#pagination--filtering", "text": "Pagination & Filtering", "status": "Anchor not found: #pagination--filtering"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#41-pagination-setup", "text": "4.1. Pa<PERSON>ation Setup", "status": "Anchor not found: #41-pagination-setup"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#42-advanced-filtering", "text": "4.2. Advanced Filtering", "status": "Anchor not found: #42-advanced-filtering"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#43-sorting--ordering", "text": "4.3. Sorting & Ordering", "status": "Anchor not found: #43-sorting--ordering"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#api-response-formatting", "text": "API Response Formatting", "status": "Anchor not found: #api-response-formatting"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#caching-integration", "text": "Caching Integration", "status": "Anchor not found: #caching-integration"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#testing-strategies", "text": "Testing Strategies", "status": "Anchor not found: #testing-strategies"}, {"file": "packages/070-laravel-fractal-guide.md", "link": "#best-practices", "text": "Best Practices", "status": "Anchor not found: #best-practices"}, {"file": "packages/090-spatie-tags-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/150-spatie-laravel-translatable-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/150-spatie-laravel-translatable-guide.md", "link": "../integration-patterns.md", "text": "Package Integration Patterns", "status": "File not found: integration-patterns.md"}, {"file": "packages/110-spatie-permission-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#12-api-installation", "text": "1.2. API Installation", "status": "Anchor not found: #12-api-installation"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#13-configuration-setup", "text": "1.3. Configuration Setup", "status": "Anchor not found: #13-configuration-setup"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#21-token-generation", "text": "2.1. Token <PERSON>", "status": "Anchor not found: #21-token-generation"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#22-token-management", "text": "2.2. Token Management", "status": "Anchor not found: #22-token-management"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#23-token-abilities", "text": "2.3. Token Abilities", "status": "Anchor not found: #23-token-abilities"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#31-csrf-protection", "text": "3.1. CSRF Protection", "status": "Anchor not found: #31-csrf-protection"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#32-session-based-auth", "text": "3.2. Session-Based Auth", "status": "Anchor not found: #32-session-based-auth"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#33-frontend-integration", "text": "3.3. Frontend Integration", "status": "Anchor not found: #33-frontend-integration"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#41-mobile-token-flow", "text": "4.1. Mobile Token Flow", "status": "Anchor not found: #41-mobile-token-flow"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#42-device-management", "text": "4.2. Device Management", "status": "Anchor not found: #42-device-management"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#43-push-notifications", "text": "4.3. Push Notifications", "status": "Anchor not found: #43-push-notifications"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#security-best-practices", "text": "Security Best Practices", "status": "Anchor not found: #security-best-practices"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#rate-limiting", "text": "Rate Limiting", "status": "Anchor not found: #rate-limiting"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#testing-strategies", "text": "Testing Strategies", "status": "Anchor not found: #testing-strategies"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "packages/080-laravel-sanctum-guide.md", "link": "#troubleshooting", "text": "Troubleshooting", "status": "Anchor not found: #troubleshooting"}, {"file": "packages/110-spatie-comments-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/110-spatie-comments-guide.md", "link": "#rbac-integration", "text": "RBAC Integration", "status": "Anchor not found: #rbac-integration"}, {"file": "packages/110-spatie-comments-guide.md", "link": "#real-time-updates", "text": "Real-time Updates", "status": "Anchor not found: #real-time-updates"}, {"file": "packages/110-spatie-comments-guide.md", "link": "#spam-prevention", "text": "Spam Prevention", "status": "Anchor not found: #spam-prevention"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#12-database-configuration", "text": "1.2. Database Configuration", "status": "Anchor not found: #12-database-configuration"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#13-environment-setup", "text": "1.3. Environment Setup", "status": "Anchor not found: #13-environment-setup"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#21-basic-dashboard-setup", "text": "2.1. Basic Dashboard Setup", "status": "Anchor not found: #21-basic-dashboard-setup"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#22-custom-dashboard-layouts", "text": "2.2. Custom Dashboard Layouts", "status": "Anchor not found: #22-custom-dashboard-layouts"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#23-authentication--authorization", "text": "2.3. Authentication & Authorization", "status": "Anchor not found: #23-authentication--authorization"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#31-built-in-recorders", "text": "3.1. Built-in Recorders", "status": "Anchor not found: #31-built-in-recorders"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#32-custom-metrics-collection", "text": "3.2. Custom Metrics Collection", "status": "Anchor not found: #32-custom-metrics-collection"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#33-performance-monitoring", "text": "3.3. Performance Monitoring", "status": "Anchor not found: #33-performance-monitoring"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#custom-metrics--cards", "text": "Custom Metrics & Cards", "status": "Anchor not found: #custom-metrics--cards"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#41-creating-custom-recorders", "text": "4.1. Creating Custom Recorders", "status": "Anchor not found: #41-creating-custom-recorders"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#42-building-custom-cards", "text": "4.2. Building Custom Cards", "status": "Anchor not found: #42-building-custom-cards"}, {"file": "packages/020-laravel-pulse-guide.md", "link": "#43-business-metrics-integration", "text": "4.3. Business Metrics Integration", "status": "Anchor not found: #43-business-metrics-integration"}, {"file": "packages/140-spatie-laravel-query-builder-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/100-laravel-query-builder-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/100-laravel-query-builder-guide.md", "link": "#sorting--pagination", "text": "Sorting & Pagination", "status": "Anchor not found: #sorting--pagination"}, {"file": "packages/100-laravel-query-builder-guide.md", "link": "#custom-filter-development", "text": "Custom Filter Development", "status": "Anchor not found: #custom-filter-development"}, {"file": "packages/100-laravel-query-builder-guide.md", "link": "#real-world-examples", "text": "Real-World Examples", "status": "Anchor not found: #real-world-examples"}, {"file": "packages/120-laravel-folio-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/120-laravel-folio-guide.md", "link": "#livewire-volt-integration", "text": "Livewire/Volt Integration", "status": "Anchor not found: #livewire-volt-integration"}, {"file": "packages/120-laravel-folio-guide.md", "link": "#production-deployment", "text": "Production Deployment", "status": "Anchor not found: #production-deployment"}, {"file": "packages/120-laravel-folio-guide.md", "link": "#real-world-examples", "text": "Real-World Examples", "status": "Anchor not found: #real-world-examples"}, {"file": "packages/060-laravel-data-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "packages/060-laravel-data-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "packages/060-laravel-data-guide.md", "link": "#12-configuration-publishing", "text": "1.2. Configuration Publishing", "status": "Anchor not found: #12-configuration-publishing"}, {"file": "packages/060-laravel-data-guide.md", "link": "#13-basic-setup", "text": "1.3. Basic Setup", "status": "Anchor not found: #13-basic-setup"}, {"file": "packages/060-laravel-data-guide.md", "link": "#21-basic-dto-creation", "text": "2.1. Basic DTO Creation", "status": "Anchor not found: #21-basic-dto-creation"}, {"file": "packages/060-laravel-data-guide.md", "link": "#22-advanced-dto-features", "text": "2.2. Advanced DTO Features", "status": "Anchor not found: #22-advanced-dto-features"}, {"file": "packages/060-laravel-data-guide.md", "link": "#23-nested-dtos", "text": "2.3. Nested DTOs", "status": "Anchor not found: #23-nested-dtos"}, {"file": "packages/060-laravel-data-guide.md", "link": "#validation--transformation", "text": "Validation & Transformation", "status": "Anchor not found: #validation--transformation"}, {"file": "packages/060-laravel-data-guide.md", "link": "#31-built-in-validation", "text": "3.1. Built-in Validation", "status": "Anchor not found: #31-built-in-validation"}, {"file": "packages/060-laravel-data-guide.md", "link": "#32-custom-validation-rules", "text": "3.2. Custom Validation Rules", "status": "Anchor not found: #32-custom-validation-rules"}, {"file": "packages/060-laravel-data-guide.md", "link": "#33-data-transformation", "text": "3.3. Data Transformation", "status": "Anchor not found: #33-data-transformation"}, {"file": "packages/060-laravel-data-guide.md", "link": "#41-api-resource-integration", "text": "4.1. API Resource Integration", "status": "Anchor not found: #41-api-resource-integration"}, {"file": "packages/060-laravel-data-guide.md", "link": "#42-request-handling", "text": "4.2. Request Handling", "status": "Anchor not found: #42-request-handling"}, {"file": "packages/060-laravel-data-guide.md", "link": "#43-response-formatting", "text": "4.3. Response Formatting", "status": "Anchor not found: #43-response-formatting"}, {"file": "packages/060-laravel-data-guide.md", "link": "#collections--arrays", "text": "Collections & Arrays", "status": "Anchor not found: #collections--arrays"}, {"file": "packages/150-spatie-activitylog-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/150-spatie-activitylog-guide.md", "link": "#security--compliance", "text": "Security & Compliance", "status": "Anchor not found: #security--compliance"}, {"file": "packages/150-spatie-activitylog-guide.md", "link": "#real-time-activity-monitoring", "text": "Real-time Activity Monitoring", "status": "Anchor not found: #real-time-activity-monitoring"}, {"file": "packages/150-spatie-activitylog-guide.md", "link": "#activity-analytics", "text": "Activity Analytics", "status": "Anchor not found: #activity-analytics"}, {"file": "packages/150-spatie-activitylog-guide.md", "link": "#integration-patterns", "text": "Integration Patterns", "status": "Anchor not found: #integration-patterns"}, {"file": "packages/150-spatie-activitylog-guide.md", "link": "#production-deployment", "text": "Production Deployment", "status": "Anchor not found: #production-deployment"}, {"file": "packages/150-spatie-activitylog-guide.md", "link": "../testing/010-pest-testing-guide.md", "text": "Modern Testing with Pest Guide", "status": "File not found: testing/010-pest-testing-guide.md"}, {"file": "packages/150-spatie-activitylog-guide.md", "link": "../development/010-debugbar-guide.md", "text": "Development Debugging Tools Guide", "status": "File not found: development/010-debugbar-guide.md"}, {"file": "packages/130-nnjeim-world-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/130-nnjeim-world-guide.md", "link": "#country-state--city-management", "text": "Country, State & City Management", "status": "Anchor not found: #country-state--city-management"}, {"file": "packages/130-nnjeim-world-guide.md", "link": "#user-profile-integration", "text": "User Profile Integration", "status": "Anchor not found: #user-profile-integration"}, {"file": "packages/130-nnjeim-world-guide.md", "link": "#business-logic-integration", "text": "Business Logic Integration", "status": "Anchor not found: #business-logic-integration"}, {"file": "packages/testing/000-testing-index.md", "link": "../development/000-development-index.md", "text": "Development Tools Index", "status": "File not found: packages/development/000-development-index.md"}, {"file": "packages/testing/010-pest-testing-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "packages/testing/010-pest-testing-guide.md", "link": "#advanced-testing-patterns", "text": "Advanced Testing Patterns", "status": "Anchor not found: #advanced-testing-patterns"}, {"file": "packages/testing/010-pest-testing-guide.md", "link": "#type-coverage", "text": "Type Coverage", "status": "Anchor not found: #type-coverage"}, {"file": "packages/testing/010-pest-testing-guide.md", "link": "#api-testing", "text": "API Testing", "status": "Anchor not found: #api-testing"}, {"file": "packages/testing/010-pest-testing-guide.md", "link": "#livewire-testing", "text": "Livewire Testing", "status": "Anchor not found: #livewire-testing"}, {"file": "packages/testing/010-pest-testing-guide.md", "link": "../development/010-debugbar-guide.md", "text": "Development Debugging Tools Guide", "status": "File not found: packages/development/010-debugbar-guide.md"}, {"file": "packages/testing/010-pest-testing-guide.md", "link": "../development/020-pint-code-quality-guide.md", "text": "Code Quality and Formatting Guide", "status": "File not found: packages/development/020-pint-code-quality-guide.md"}, {"file": "packages/testing/010-pest-testing-guide.md", "link": "../packages/150-spatie-activitylog-guide.md", "text": "Enhanced Spatie ActivityLog Guide", "status": "File not found: packages/packages/150-spatie-activitylog-guide.md"}], "resolved_issues": [], "status": "FAIL", "execution_time": 0.6785039901733398}, {"timestamp": "2025-07-08T02:10:00.902017", "total_files": 17, "total_links": 294, "broken_links": 84, "success_rate": 71.42857142857143, "critical_files_status": {"000-chinook-index.md": -1, "050-chinook-advanced-features-guide.md": -1, "060-chinook-media-library-guide.md": -1, "070-chinook-hierarchy-comparison-guide.md": -1, "filament/setup/000-index.md": -1, "filament/resources/000-index.md": -1, "packages/000-packages-index.md": -1, "testing/000-testing-index.md": -1}, "new_issues": [{"file": "030-tracks-resource.md", "link": "#form-configuration", "text": "Form Configuration", "status": "Anchor not found: #form-configuration"}, {"file": "030-tracks-resource.md", "link": "#table-configuration", "text": "Table Configuration", "status": "Anchor not found: #table-configuration"}, {"file": "030-tracks-resource.md", "link": "#complex-relationships", "text": "Complex Relationships", "status": "Anchor not found: #complex-relationships"}, {"file": "030-tracks-resource.md", "link": "#album-relationship", "text": "Album Relationship", "status": "Anchor not found: #album-relationship"}, {"file": "030-tracks-resource.md", "link": "#media-type-relationship", "text": "Media Type Relationship", "status": "Anchor not found: #media-type-relationship"}, {"file": "030-tracks-resource.md", "link": "#invoice-lines-relationship", "text": "Invoice Lines Relationship", "status": "Anchor not found: #invoice-lines-relationship"}, {"file": "030-tracks-resource.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "030-tracks-resource.md", "link": "#audio-file-management", "text": "Audio File Management", "status": "Anchor not found: #audio-file-management"}, {"file": "030-tracks-resource.md", "link": "#pricing-and-sales", "text": "Pricing and Sales", "status": "Anchor not found: #pricing-and-sales"}, {"file": "030-tracks-resource.md", "link": "#playlist-integration", "text": "Playlist Integration", "status": "Anchor not found: #playlist-integration"}, {"file": "030-tracks-resource.md", "link": "#business-logic", "text": "Business Logic", "status": "Anchor not found: #business-logic"}, {"file": "030-tracks-resource.md", "link": "#track-validation", "text": "Track Validation", "status": "Anchor not found: #track-validation"}, {"file": "030-tracks-resource.md", "link": "#duration-handling", "text": "Duration Handling", "status": "Anchor not found: #duration-handling"}, {"file": "030-tracks-resource.md", "link": "#sales-analytics", "text": "Sales Analytics", "status": "Anchor not found: #sales-analytics"}, {"file": "030-tracks-resource.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "030-tracks-resource.md", "link": "#authorization", "text": "Authorization", "status": "Anchor not found: #authorization"}, {"file": "030-tracks-resource.md", "link": "#testing", "text": "Testing", "status": "Anchor not found: #testing"}, {"file": "140-bulk-operations.md", "link": "#progress-tracking", "text": "Progress Tracking", "status": "Anchor not found: #progress-tracking"}, {"file": "140-bulk-operations.md", "link": "../deployment/000-index.md", "text": "Filament Deployment Series", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../deployment/000-index.md"}, {"file": "140-bulk-operations.md", "link": "../performance/bulk-processing.md", "text": "Performance Optimization", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../performance/bulk-processing.md"}, {"file": "140-bulk-operations.md", "link": "../error-handling/bulk-operations.md", "text": "Erro<PERSON>", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../error-handling/bulk-operations.md"}, {"file": "120-form-components.md", "link": "../forms/validation.md", "text": "Validation Strategies", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../forms/validation.md"}, {"file": "120-form-components.md", "link": "../ui/component-library.md", "text": "UI Components", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../ui/component-library.md"}, {"file": "080-invoices-resource.md", "link": "../features/010-dashboard-configuration.md", "text": "Dashboard Configuration", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features/010-dashboard-configuration.md"}, {"file": "120-relationship-managers.md", "link": "130-form-components.md", "text": "Form Components", "status": "File not found: 130-form-components.md"}, {"file": "070-customers-resource.md", "link": "../setup/050-security-configuration.md", "text": "Security Configuration", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../setup/050-security-configuration.md"}, {"file": "070-customers-resource.md", "link": "../features/010-dashboard-configuration.md", "text": "Dashboard Configuration", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features/010-dashboard-configuration.md"}, {"file": "README.md", "link": "130-form-components.md", "text": "Form Components", "status": "File not found: 130-form-components.md"}, {"file": "README.md", "link": "140-table-features.md", "text": "Table Features", "status": "File not found: 140-table-features.md"}, {"file": "README.md", "link": "150-bulk-operations.md", "text": "Bulk Operations", "status": "File not found: 150-bulk-operations.md"}, {"file": "README.md", "link": "../setup/", "text": "Setup Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../setup"}, {"file": "README.md", "link": "../features/", "text": "Features Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features"}, {"file": "README.md", "link": "../testing/", "text": "Testing Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../testing"}, {"file": "README.md", "link": "../deployment/", "text": "Deployment Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../deployment"}, {"file": "020-albums-resource.md", "link": "#form-configuration", "text": "Form Configuration", "status": "Anchor not found: #form-configuration"}, {"file": "020-albums-resource.md", "link": "#table-configuration", "text": "Table Configuration", "status": "Anchor not found: #table-configuration"}, {"file": "020-albums-resource.md", "link": "#categories-relationship-manager", "text": "Categories Relationship Manager", "status": "Anchor not found: #categories-relationship-manager"}, {"file": "020-albums-resource.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "020-albums-resource.md", "link": "#custom-form-components", "text": "Custom Form Components", "status": "Anchor not found: #custom-form-components"}, {"file": "020-albums-resource.md", "link": "#advanced-table-features", "text": "Advanced Table Features", "status": "Anchor not found: #advanced-table-features"}, {"file": "020-albums-resource.md", "link": "#bulk-operations", "text": "Bulk Operations", "status": "Anchor not found: #bulk-operations"}, {"file": "020-albums-resource.md", "link": "#authorization", "text": "Authorization", "status": "Anchor not found: #authorization"}, {"file": "020-albums-resource.md", "link": "#resource-level-authorization", "text": "Resource-Level Authorization", "status": "Anchor not found: #resource-level-authorization"}, {"file": "020-albums-resource.md", "link": "#field-level-security", "text": "Field-Level Security", "status": "Anchor not found: #field-level-security"}, {"file": "020-albums-resource.md", "link": "#business-logic", "text": "Business Logic", "status": "Anchor not found: #business-logic"}, {"file": "020-albums-resource.md", "link": "#album-validation", "text": "Album Validation", "status": "Anchor not found: #album-validation"}, {"file": "020-albums-resource.md", "link": "#release-date-handling", "text": "Release Date Handling", "status": "Anchor not found: #release-date-handling"}, {"file": "020-albums-resource.md", "link": "#cover-art-management", "text": "Cover Art Management", "status": "Anchor not found: #cover-art-management"}, {"file": "020-albums-resource.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "020-albums-resource.md", "link": "#testing", "text": "Testing", "status": "Anchor not found: #testing"}, {"file": "040-categories-resource.md", "link": "#hierarchical-form-configuration", "text": "Hierarchical Form Configuration", "status": "Anchor not found: #hierarchical-form-configuration"}, {"file": "040-categories-resource.md", "link": "#tree-table-configuration", "text": "Tree Table Configuration", "status": "Anchor not found: #tree-table-configuration"}, {"file": "040-categories-resource.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "040-categories-resource.md", "link": "#polymorphic-relationships", "text": "Polymorphic Relationships", "status": "Anchor not found: #polymorphic-relationships"}, {"file": "040-categories-resource.md", "link": "#categorizable-implementation", "text": "Categorizable Implementation", "status": "Anchor not found: #categorizable-implementation"}, {"file": "040-categories-resource.md", "link": "#multi-model-assignment", "text": "Multi-Model Assignment", "status": "Anchor not found: #multi-model-assignment"}, {"file": "040-categories-resource.md", "link": "#category-types", "text": "Category Types", "status": "Anchor not found: #category-types"}, {"file": "040-categories-resource.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "040-categories-resource.md", "link": "#category-tree-visualization", "text": "Category Tree Visualization", "status": "Anchor not found: #category-tree-visualization"}, {"file": "040-categories-resource.md", "link": "#bulk-category-operations", "text": "Bulk Category Operations", "status": "Anchor not found: #bulk-category-operations"}, {"file": "040-categories-resource.md", "link": "#category-analytics", "text": "Category Analytics", "status": "Anchor not found: #category-analytics"}, {"file": "040-categories-resource.md", "link": "#business-logic", "text": "Business Logic", "status": "Anchor not found: #business-logic"}, {"file": "040-categories-resource.md", "link": "#category-validation", "text": "Category Validation", "status": "Anchor not found: #category-validation"}, {"file": "040-categories-resource.md", "link": "#hierarchy-constraints", "text": "Hierarchy Constraints", "status": "Anchor not found: #hierarchy-constraints"}, {"file": "040-categories-resource.md", "link": "#usage-tracking", "text": "Usage Tracking", "status": "Anchor not found: #usage-tracking"}, {"file": "040-categories-resource.md", "link": "#authorization", "text": "Authorization", "status": "Anchor not found: #authorization"}, {"file": "040-categories-resource.md", "link": "#testing", "text": "Testing", "status": "Anchor not found: #testing"}, {"file": "130-table-features.md", "link": "#sorting-and-grouping", "text": "Sorting and Grouping", "status": "Anchor not found: #sorting-and-grouping"}, {"file": "130-table-features.md", "link": "../performance/query-optimization.md", "text": "Performance Optimization", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../performance/query-optimization.md"}, {"file": "130-table-features.md", "link": "../ui/table-components.md", "text": "UI Components", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../ui/table-components.md"}, {"file": "090-invoice-lines-resource.md", "link": "../business/pricing-models.md", "text": "Pricing Strategies", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../business/pricing-models.md"}, {"file": "090-invoice-lines-resource.md", "link": "../analytics/sales-reporting.md", "text": "Sales Analytics", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../analytics/sales-reporting.md"}, {"file": "100-employees-resource.md", "link": "../security/role-management.md", "text": "Role Management", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../security/role-management.md"}, {"file": "100-employees-resource.md", "link": "../analytics/hr-metrics.md", "text": "HR Analytics", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../analytics/hr-metrics.md"}, {"file": "110-users-resource.md", "link": "../security/authentication.md", "text": "Authentication Systems", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../security/authentication.md"}, {"file": "110-users-resource.md", "link": "../analytics/user-metrics.md", "text": "User Analytics", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../analytics/user-metrics.md"}, {"file": "000-index.md", "link": "#customer-management", "text": "Customer Management", "status": "Anchor not found: #customer-management"}, {"file": "000-index.md", "link": "#sales--invoicing", "text": "Sales & Invoicing", "status": "Anchor not found: #sales--invoicing"}, {"file": "000-index.md", "link": "../../010-chinook-models-guide.md", "text": "Chinook Models Guide", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../../010-chinook-models-guide.md"}, {"file": "000-index.md", "link": "../setup/000-index.md", "text": "Filament Setup Guide", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../setup/000-index.md"}, {"file": "000-index.md", "link": "../features/000-index.md", "text": "Filament Features", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features/000-index.md"}, {"file": "000-index.md", "link": "../testing/000-index.md", "text": "Testing Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../testing/000-index.md"}, {"file": "000-index.md", "link": "../README.md", "text": "Filament Documentation Index", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../README.md"}, {"file": "060-media-types-resource.md", "link": "../testing/010-testing-strategy.md", "text": "Testing Strategy", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../testing/010-testing-strategy.md"}], "resolved_issues": [], "status": "WARN", "execution_time": 0.08473610877990723}, {"timestamp": "2025-07-08T02:12:24.053789", "total_files": 17, "total_links": 294, "broken_links": 67, "success_rate": 77.21088435374149, "critical_files_status": {"000-chinook-index.md": -1, "050-chinook-advanced-features-guide.md": -1, "060-chinook-media-library-guide.md": -1, "070-chinook-hierarchy-comparison-guide.md": -1, "filament/setup/000-index.md": -1, "filament/resources/000-index.md": -1, "packages/000-packages-index.md": -1, "testing/000-testing-index.md": -1}, "new_issues": [{"file": "140-bulk-operations.md", "link": "#progress-tracking", "text": "Progress Tracking", "status": "Anchor not found: #progress-tracking"}, {"file": "140-bulk-operations.md", "link": "../deployment/000-index.md", "text": "Filament Deployment Series", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../deployment/000-index.md"}, {"file": "140-bulk-operations.md", "link": "../performance/bulk-processing.md", "text": "Performance Optimization", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../performance/bulk-processing.md"}, {"file": "140-bulk-operations.md", "link": "../error-handling/bulk-operations.md", "text": "Erro<PERSON>", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../error-handling/bulk-operations.md"}, {"file": "120-form-components.md", "link": "../forms/validation.md", "text": "Validation Strategies", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../forms/validation.md"}, {"file": "120-form-components.md", "link": "../ui/component-library.md", "text": "UI Components", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../ui/component-library.md"}, {"file": "080-invoices-resource.md", "link": "../features/010-dashboard-configuration.md", "text": "Dashboard Configuration", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features/010-dashboard-configuration.md"}, {"file": "120-relationship-managers.md", "link": "130-form-components.md", "text": "Form Components", "status": "File not found: 130-form-components.md"}, {"file": "070-customers-resource.md", "link": "../setup/050-security-configuration.md", "text": "Security Configuration", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../setup/050-security-configuration.md"}, {"file": "070-customers-resource.md", "link": "../features/010-dashboard-configuration.md", "text": "Dashboard Configuration", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features/010-dashboard-configuration.md"}, {"file": "README.md", "link": "130-form-components.md", "text": "Form Components", "status": "File not found: 130-form-components.md"}, {"file": "README.md", "link": "140-table-features.md", "text": "Table Features", "status": "File not found: 140-table-features.md"}, {"file": "README.md", "link": "150-bulk-operations.md", "text": "Bulk Operations", "status": "File not found: 150-bulk-operations.md"}, {"file": "README.md", "link": "../setup/", "text": "Setup Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../setup"}, {"file": "README.md", "link": "../features/", "text": "Features Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features"}, {"file": "README.md", "link": "../testing/", "text": "Testing Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../testing"}, {"file": "README.md", "link": "../deployment/", "text": "Deployment Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../deployment"}, {"file": "020-albums-resource.md", "link": "#form-configuration", "text": "Form Configuration", "status": "Anchor not found: #form-configuration"}, {"file": "020-albums-resource.md", "link": "#table-configuration", "text": "Table Configuration", "status": "Anchor not found: #table-configuration"}, {"file": "020-albums-resource.md", "link": "#categories-relationship-manager", "text": "Categories Relationship Manager", "status": "Anchor not found: #categories-relationship-manager"}, {"file": "020-albums-resource.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "020-albums-resource.md", "link": "#custom-form-components", "text": "Custom Form Components", "status": "Anchor not found: #custom-form-components"}, {"file": "020-albums-resource.md", "link": "#advanced-table-features", "text": "Advanced Table Features", "status": "Anchor not found: #advanced-table-features"}, {"file": "020-albums-resource.md", "link": "#bulk-operations", "text": "Bulk Operations", "status": "Anchor not found: #bulk-operations"}, {"file": "020-albums-resource.md", "link": "#authorization", "text": "Authorization", "status": "Anchor not found: #authorization"}, {"file": "020-albums-resource.md", "link": "#resource-level-authorization", "text": "Resource-Level Authorization", "status": "Anchor not found: #resource-level-authorization"}, {"file": "020-albums-resource.md", "link": "#field-level-security", "text": "Field-Level Security", "status": "Anchor not found: #field-level-security"}, {"file": "020-albums-resource.md", "link": "#business-logic", "text": "Business Logic", "status": "Anchor not found: #business-logic"}, {"file": "020-albums-resource.md", "link": "#album-validation", "text": "Album Validation", "status": "Anchor not found: #album-validation"}, {"file": "020-albums-resource.md", "link": "#release-date-handling", "text": "Release Date Handling", "status": "Anchor not found: #release-date-handling"}, {"file": "020-albums-resource.md", "link": "#cover-art-management", "text": "Cover Art Management", "status": "Anchor not found: #cover-art-management"}, {"file": "020-albums-resource.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "020-albums-resource.md", "link": "#testing", "text": "Testing", "status": "Anchor not found: #testing"}, {"file": "040-categories-resource.md", "link": "#hierarchical-form-configuration", "text": "Hierarchical Form Configuration", "status": "Anchor not found: #hierarchical-form-configuration"}, {"file": "040-categories-resource.md", "link": "#tree-table-configuration", "text": "Tree Table Configuration", "status": "Anchor not found: #tree-table-configuration"}, {"file": "040-categories-resource.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "040-categories-resource.md", "link": "#polymorphic-relationships", "text": "Polymorphic Relationships", "status": "Anchor not found: #polymorphic-relationships"}, {"file": "040-categories-resource.md", "link": "#categorizable-implementation", "text": "Categorizable Implementation", "status": "Anchor not found: #categorizable-implementation"}, {"file": "040-categories-resource.md", "link": "#multi-model-assignment", "text": "Multi-Model Assignment", "status": "Anchor not found: #multi-model-assignment"}, {"file": "040-categories-resource.md", "link": "#category-types", "text": "Category Types", "status": "Anchor not found: #category-types"}, {"file": "040-categories-resource.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "040-categories-resource.md", "link": "#category-tree-visualization", "text": "Category Tree Visualization", "status": "Anchor not found: #category-tree-visualization"}, {"file": "040-categories-resource.md", "link": "#bulk-category-operations", "text": "Bulk Category Operations", "status": "Anchor not found: #bulk-category-operations"}, {"file": "040-categories-resource.md", "link": "#category-analytics", "text": "Category Analytics", "status": "Anchor not found: #category-analytics"}, {"file": "040-categories-resource.md", "link": "#business-logic", "text": "Business Logic", "status": "Anchor not found: #business-logic"}, {"file": "040-categories-resource.md", "link": "#category-validation", "text": "Category Validation", "status": "Anchor not found: #category-validation"}, {"file": "040-categories-resource.md", "link": "#hierarchy-constraints", "text": "Hierarchy Constraints", "status": "Anchor not found: #hierarchy-constraints"}, {"file": "040-categories-resource.md", "link": "#usage-tracking", "text": "Usage Tracking", "status": "Anchor not found: #usage-tracking"}, {"file": "040-categories-resource.md", "link": "#authorization", "text": "Authorization", "status": "Anchor not found: #authorization"}, {"file": "040-categories-resource.md", "link": "#testing", "text": "Testing", "status": "Anchor not found: #testing"}, {"file": "130-table-features.md", "link": "#sorting-and-grouping", "text": "Sorting and Grouping", "status": "Anchor not found: #sorting-and-grouping"}, {"file": "130-table-features.md", "link": "../performance/query-optimization.md", "text": "Performance Optimization", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../performance/query-optimization.md"}, {"file": "130-table-features.md", "link": "../ui/table-components.md", "text": "UI Components", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../ui/table-components.md"}, {"file": "090-invoice-lines-resource.md", "link": "../business/pricing-models.md", "text": "Pricing Strategies", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../business/pricing-models.md"}, {"file": "090-invoice-lines-resource.md", "link": "../analytics/sales-reporting.md", "text": "Sales Analytics", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../analytics/sales-reporting.md"}, {"file": "100-employees-resource.md", "link": "../security/role-management.md", "text": "Role Management", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../security/role-management.md"}, {"file": "100-employees-resource.md", "link": "../analytics/hr-metrics.md", "text": "HR Analytics", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../analytics/hr-metrics.md"}, {"file": "110-users-resource.md", "link": "../security/authentication.md", "text": "Authentication Systems", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../security/authentication.md"}, {"file": "110-users-resource.md", "link": "../analytics/user-metrics.md", "text": "User Analytics", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../analytics/user-metrics.md"}, {"file": "000-index.md", "link": "#customer-management", "text": "Customer Management", "status": "Anchor not found: #customer-management"}, {"file": "000-index.md", "link": "#sales--invoicing", "text": "Sales & Invoicing", "status": "Anchor not found: #sales--invoicing"}, {"file": "000-index.md", "link": "../../010-chinook-models-guide.md", "text": "Chinook Models Guide", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../../010-chinook-models-guide.md"}, {"file": "000-index.md", "link": "../setup/000-index.md", "text": "Filament Setup Guide", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../setup/000-index.md"}, {"file": "000-index.md", "link": "../features/000-index.md", "text": "Filament Features", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features/000-index.md"}, {"file": "000-index.md", "link": "../testing/000-index.md", "text": "Testing Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../testing/000-index.md"}, {"file": "000-index.md", "link": "../README.md", "text": "Filament Documentation Index", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../README.md"}, {"file": "060-media-types-resource.md", "link": "../testing/010-testing-strategy.md", "text": "Testing Strategy", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../testing/010-testing-strategy.md"}], "resolved_issues": [], "status": "PASS", "execution_time": 0.12606501579284668}, {"timestamp": "2025-07-08T02:13:55.301402", "total_files": 17, "total_links": 294, "broken_links": 62, "success_rate": 78.91156462585033, "critical_files_status": {"000-chinook-index.md": -1, "050-chinook-advanced-features-guide.md": -1, "060-chinook-media-library-guide.md": -1, "070-chinook-hierarchy-comparison-guide.md": -1, "filament/setup/000-index.md": -1, "filament/resources/000-index.md": -1, "packages/000-packages-index.md": -1, "testing/000-testing-index.md": -1}, "new_issues": [{"file": "140-bulk-operations.md", "link": "../deployment/000-index.md", "text": "Filament Deployment Series", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../deployment/000-index.md"}, {"file": "140-bulk-operations.md", "link": "../performance/bulk-processing.md", "text": "Performance Optimization", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../performance/bulk-processing.md"}, {"file": "140-bulk-operations.md", "link": "../error-handling/bulk-operations.md", "text": "Erro<PERSON>", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../error-handling/bulk-operations.md"}, {"file": "120-form-components.md", "link": "../forms/validation.md", "text": "Validation Strategies", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../forms/validation.md"}, {"file": "120-form-components.md", "link": "../ui/component-library.md", "text": "UI Components", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../ui/component-library.md"}, {"file": "080-invoices-resource.md", "link": "../features/010-dashboard-configuration.md", "text": "Dashboard Configuration", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features/010-dashboard-configuration.md"}, {"file": "070-customers-resource.md", "link": "../setup/050-security-configuration.md", "text": "Security Configuration", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../setup/050-security-configuration.md"}, {"file": "070-customers-resource.md", "link": "../features/010-dashboard-configuration.md", "text": "Dashboard Configuration", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features/010-dashboard-configuration.md"}, {"file": "README.md", "link": "../setup/", "text": "Setup Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../setup"}, {"file": "README.md", "link": "../features/", "text": "Features Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features"}, {"file": "README.md", "link": "../testing/", "text": "Testing Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../testing"}, {"file": "README.md", "link": "../deployment/", "text": "Deployment Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../deployment"}, {"file": "020-albums-resource.md", "link": "#form-configuration", "text": "Form Configuration", "status": "Anchor not found: #form-configuration"}, {"file": "020-albums-resource.md", "link": "#table-configuration", "text": "Table Configuration", "status": "Anchor not found: #table-configuration"}, {"file": "020-albums-resource.md", "link": "#categories-relationship-manager", "text": "Categories Relationship Manager", "status": "Anchor not found: #categories-relationship-manager"}, {"file": "020-albums-resource.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "020-albums-resource.md", "link": "#custom-form-components", "text": "Custom Form Components", "status": "Anchor not found: #custom-form-components"}, {"file": "020-albums-resource.md", "link": "#advanced-table-features", "text": "Advanced Table Features", "status": "Anchor not found: #advanced-table-features"}, {"file": "020-albums-resource.md", "link": "#bulk-operations", "text": "Bulk Operations", "status": "Anchor not found: #bulk-operations"}, {"file": "020-albums-resource.md", "link": "#authorization", "text": "Authorization", "status": "Anchor not found: #authorization"}, {"file": "020-albums-resource.md", "link": "#resource-level-authorization", "text": "Resource-Level Authorization", "status": "Anchor not found: #resource-level-authorization"}, {"file": "020-albums-resource.md", "link": "#field-level-security", "text": "Field-Level Security", "status": "Anchor not found: #field-level-security"}, {"file": "020-albums-resource.md", "link": "#business-logic", "text": "Business Logic", "status": "Anchor not found: #business-logic"}, {"file": "020-albums-resource.md", "link": "#album-validation", "text": "Album Validation", "status": "Anchor not found: #album-validation"}, {"file": "020-albums-resource.md", "link": "#release-date-handling", "text": "Release Date Handling", "status": "Anchor not found: #release-date-handling"}, {"file": "020-albums-resource.md", "link": "#cover-art-management", "text": "Cover Art Management", "status": "Anchor not found: #cover-art-management"}, {"file": "020-albums-resource.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "020-albums-resource.md", "link": "#testing", "text": "Testing", "status": "Anchor not found: #testing"}, {"file": "040-categories-resource.md", "link": "#hierarchical-form-configuration", "text": "Hierarchical Form Configuration", "status": "Anchor not found: #hierarchical-form-configuration"}, {"file": "040-categories-resource.md", "link": "#tree-table-configuration", "text": "Tree Table Configuration", "status": "Anchor not found: #tree-table-configuration"}, {"file": "040-categories-resource.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "040-categories-resource.md", "link": "#polymorphic-relationships", "text": "Polymorphic Relationships", "status": "Anchor not found: #polymorphic-relationships"}, {"file": "040-categories-resource.md", "link": "#categorizable-implementation", "text": "Categorizable Implementation", "status": "Anchor not found: #categorizable-implementation"}, {"file": "040-categories-resource.md", "link": "#multi-model-assignment", "text": "Multi-Model Assignment", "status": "Anchor not found: #multi-model-assignment"}, {"file": "040-categories-resource.md", "link": "#category-types", "text": "Category Types", "status": "Anchor not found: #category-types"}, {"file": "040-categories-resource.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "040-categories-resource.md", "link": "#category-tree-visualization", "text": "Category Tree Visualization", "status": "Anchor not found: #category-tree-visualization"}, {"file": "040-categories-resource.md", "link": "#bulk-category-operations", "text": "Bulk Category Operations", "status": "Anchor not found: #bulk-category-operations"}, {"file": "040-categories-resource.md", "link": "#category-analytics", "text": "Category Analytics", "status": "Anchor not found: #category-analytics"}, {"file": "040-categories-resource.md", "link": "#business-logic", "text": "Business Logic", "status": "Anchor not found: #business-logic"}, {"file": "040-categories-resource.md", "link": "#category-validation", "text": "Category Validation", "status": "Anchor not found: #category-validation"}, {"file": "040-categories-resource.md", "link": "#hierarchy-constraints", "text": "Hierarchy Constraints", "status": "Anchor not found: #hierarchy-constraints"}, {"file": "040-categories-resource.md", "link": "#usage-tracking", "text": "Usage Tracking", "status": "Anchor not found: #usage-tracking"}, {"file": "040-categories-resource.md", "link": "#authorization", "text": "Authorization", "status": "Anchor not found: #authorization"}, {"file": "040-categories-resource.md", "link": "#testing", "text": "Testing", "status": "Anchor not found: #testing"}, {"file": "130-table-features.md", "link": "#sorting-and-grouping", "text": "Sorting and Grouping", "status": "Anchor not found: #sorting-and-grouping"}, {"file": "130-table-features.md", "link": "../performance/query-optimization.md", "text": "Performance Optimization", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../performance/query-optimization.md"}, {"file": "130-table-features.md", "link": "../ui/table-components.md", "text": "UI Components", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../ui/table-components.md"}, {"file": "090-invoice-lines-resource.md", "link": "../business/pricing-models.md", "text": "Pricing Strategies", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../business/pricing-models.md"}, {"file": "090-invoice-lines-resource.md", "link": "../analytics/sales-reporting.md", "text": "Sales Analytics", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../analytics/sales-reporting.md"}, {"file": "100-employees-resource.md", "link": "../security/role-management.md", "text": "Role Management", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../security/role-management.md"}, {"file": "100-employees-resource.md", "link": "../analytics/hr-metrics.md", "text": "HR Analytics", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../analytics/hr-metrics.md"}, {"file": "110-users-resource.md", "link": "../security/authentication.md", "text": "Authentication Systems", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../security/authentication.md"}, {"file": "110-users-resource.md", "link": "../analytics/user-metrics.md", "text": "User Analytics", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../analytics/user-metrics.md"}, {"file": "000-index.md", "link": "#customer-management", "text": "Customer Management", "status": "Anchor not found: #customer-management"}, {"file": "000-index.md", "link": "#sales--invoicing", "text": "Sales & Invoicing", "status": "Anchor not found: #sales--invoicing"}, {"file": "000-index.md", "link": "../../010-chinook-models-guide.md", "text": "Chinook Models Guide", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../../010-chinook-models-guide.md"}, {"file": "000-index.md", "link": "../setup/000-index.md", "text": "Filament Setup Guide", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../setup/000-index.md"}, {"file": "000-index.md", "link": "../features/000-index.md", "text": "Filament Features", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features/000-index.md"}, {"file": "000-index.md", "link": "../testing/000-index.md", "text": "Testing Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../testing/000-index.md"}, {"file": "000-index.md", "link": "../README.md", "text": "Filament Documentation Index", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../README.md"}, {"file": "060-media-types-resource.md", "link": "../testing/010-testing-strategy.md", "text": "Testing Strategy", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../testing/010-testing-strategy.md"}], "resolved_issues": [], "status": "PASS", "execution_time": 0.06554388999938965}, {"timestamp": "2025-07-08T02:15:11.917160", "total_files": 17, "total_links": 294, "broken_links": 46, "success_rate": 84.35374149659864, "critical_files_status": {"000-chinook-index.md": -1, "050-chinook-advanced-features-guide.md": -1, "060-chinook-media-library-guide.md": -1, "070-chinook-hierarchy-comparison-guide.md": -1, "filament/setup/000-index.md": -1, "filament/resources/000-index.md": -1, "packages/000-packages-index.md": -1, "testing/000-testing-index.md": -1}, "new_issues": [{"file": "140-bulk-operations.md", "link": "../deployment/000-index.md", "text": "Filament Deployment Series", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../deployment/000-index.md"}, {"file": "140-bulk-operations.md", "link": "../performance/bulk-processing.md", "text": "Performance Optimization", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../performance/bulk-processing.md"}, {"file": "140-bulk-operations.md", "link": "../error-handling/bulk-operations.md", "text": "Erro<PERSON>", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../error-handling/bulk-operations.md"}, {"file": "120-form-components.md", "link": "../forms/validation.md", "text": "Validation Strategies", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../forms/validation.md"}, {"file": "120-form-components.md", "link": "../ui/component-library.md", "text": "UI Components", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../ui/component-library.md"}, {"file": "080-invoices-resource.md", "link": "../features/010-dashboard-configuration.md", "text": "Dashboard Configuration", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features/010-dashboard-configuration.md"}, {"file": "070-customers-resource.md", "link": "../setup/050-security-configuration.md", "text": "Security Configuration", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../setup/050-security-configuration.md"}, {"file": "070-customers-resource.md", "link": "../features/010-dashboard-configuration.md", "text": "Dashboard Configuration", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features/010-dashboard-configuration.md"}, {"file": "README.md", "link": "../setup/", "text": "Setup Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../setup"}, {"file": "README.md", "link": "../features/", "text": "Features Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features"}, {"file": "README.md", "link": "../testing/", "text": "Testing Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../testing"}, {"file": "README.md", "link": "../deployment/", "text": "Deployment Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../deployment"}, {"file": "040-categories-resource.md", "link": "#hierarchical-form-configuration", "text": "Hierarchical Form Configuration", "status": "Anchor not found: #hierarchical-form-configuration"}, {"file": "040-categories-resource.md", "link": "#tree-table-configuration", "text": "Tree Table Configuration", "status": "Anchor not found: #tree-table-configuration"}, {"file": "040-categories-resource.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "040-categories-resource.md", "link": "#polymorphic-relationships", "text": "Polymorphic Relationships", "status": "Anchor not found: #polymorphic-relationships"}, {"file": "040-categories-resource.md", "link": "#categorizable-implementation", "text": "Categorizable Implementation", "status": "Anchor not found: #categorizable-implementation"}, {"file": "040-categories-resource.md", "link": "#multi-model-assignment", "text": "Multi-Model Assignment", "status": "Anchor not found: #multi-model-assignment"}, {"file": "040-categories-resource.md", "link": "#category-types", "text": "Category Types", "status": "Anchor not found: #category-types"}, {"file": "040-categories-resource.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "040-categories-resource.md", "link": "#category-tree-visualization", "text": "Category Tree Visualization", "status": "Anchor not found: #category-tree-visualization"}, {"file": "040-categories-resource.md", "link": "#bulk-category-operations", "text": "Bulk Category Operations", "status": "Anchor not found: #bulk-category-operations"}, {"file": "040-categories-resource.md", "link": "#category-analytics", "text": "Category Analytics", "status": "Anchor not found: #category-analytics"}, {"file": "040-categories-resource.md", "link": "#business-logic", "text": "Business Logic", "status": "Anchor not found: #business-logic"}, {"file": "040-categories-resource.md", "link": "#category-validation", "text": "Category Validation", "status": "Anchor not found: #category-validation"}, {"file": "040-categories-resource.md", "link": "#hierarchy-constraints", "text": "Hierarchy Constraints", "status": "Anchor not found: #hierarchy-constraints"}, {"file": "040-categories-resource.md", "link": "#usage-tracking", "text": "Usage Tracking", "status": "Anchor not found: #usage-tracking"}, {"file": "040-categories-resource.md", "link": "#authorization", "text": "Authorization", "status": "Anchor not found: #authorization"}, {"file": "040-categories-resource.md", "link": "#testing", "text": "Testing", "status": "Anchor not found: #testing"}, {"file": "130-table-features.md", "link": "#sorting-and-grouping", "text": "Sorting and Grouping", "status": "Anchor not found: #sorting-and-grouping"}, {"file": "130-table-features.md", "link": "../performance/query-optimization.md", "text": "Performance Optimization", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../performance/query-optimization.md"}, {"file": "130-table-features.md", "link": "../ui/table-components.md", "text": "UI Components", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../ui/table-components.md"}, {"file": "090-invoice-lines-resource.md", "link": "../business/pricing-models.md", "text": "Pricing Strategies", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../business/pricing-models.md"}, {"file": "090-invoice-lines-resource.md", "link": "../analytics/sales-reporting.md", "text": "Sales Analytics", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../analytics/sales-reporting.md"}, {"file": "100-employees-resource.md", "link": "../security/role-management.md", "text": "Role Management", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../security/role-management.md"}, {"file": "100-employees-resource.md", "link": "../analytics/hr-metrics.md", "text": "HR Analytics", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../analytics/hr-metrics.md"}, {"file": "110-users-resource.md", "link": "../security/authentication.md", "text": "Authentication Systems", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../security/authentication.md"}, {"file": "110-users-resource.md", "link": "../analytics/user-metrics.md", "text": "User Analytics", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../analytics/user-metrics.md"}, {"file": "000-index.md", "link": "#customer-management", "text": "Customer Management", "status": "Anchor not found: #customer-management"}, {"file": "000-index.md", "link": "#sales--invoicing", "text": "Sales & Invoicing", "status": "Anchor not found: #sales--invoicing"}, {"file": "000-index.md", "link": "../../010-chinook-models-guide.md", "text": "Chinook Models Guide", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../../010-chinook-models-guide.md"}, {"file": "000-index.md", "link": "../setup/000-index.md", "text": "Filament Setup Guide", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../setup/000-index.md"}, {"file": "000-index.md", "link": "../features/000-index.md", "text": "Filament Features", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features/000-index.md"}, {"file": "000-index.md", "link": "../testing/000-index.md", "text": "Testing Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../testing/000-index.md"}, {"file": "000-index.md", "link": "../README.md", "text": "Filament Documentation Index", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../README.md"}, {"file": "060-media-types-resource.md", "link": "../testing/010-testing-strategy.md", "text": "Testing Strategy", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../testing/010-testing-strategy.md"}], "resolved_issues": [], "status": "PASS", "execution_time": 0.07033419609069824}, {"timestamp": "2025-07-08T02:15:59.180340", "total_files": 17, "total_links": 294, "broken_links": 29, "success_rate": 90.1360544217687, "critical_files_status": {"000-chinook-index.md": -1, "050-chinook-advanced-features-guide.md": -1, "060-chinook-media-library-guide.md": -1, "070-chinook-hierarchy-comparison-guide.md": -1, "filament/setup/000-index.md": -1, "filament/resources/000-index.md": -1, "packages/000-packages-index.md": -1, "testing/000-testing-index.md": -1}, "new_issues": [{"file": "140-bulk-operations.md", "link": "../deployment/000-index.md", "text": "Filament Deployment Series", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../deployment/000-index.md"}, {"file": "140-bulk-operations.md", "link": "../performance/bulk-processing.md", "text": "Performance Optimization", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../performance/bulk-processing.md"}, {"file": "140-bulk-operations.md", "link": "../error-handling/bulk-operations.md", "text": "Erro<PERSON>", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../error-handling/bulk-operations.md"}, {"file": "120-form-components.md", "link": "../forms/validation.md", "text": "Validation Strategies", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../forms/validation.md"}, {"file": "120-form-components.md", "link": "../ui/component-library.md", "text": "UI Components", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../ui/component-library.md"}, {"file": "080-invoices-resource.md", "link": "../features/010-dashboard-configuration.md", "text": "Dashboard Configuration", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features/010-dashboard-configuration.md"}, {"file": "070-customers-resource.md", "link": "../setup/050-security-configuration.md", "text": "Security Configuration", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../setup/050-security-configuration.md"}, {"file": "070-customers-resource.md", "link": "../features/010-dashboard-configuration.md", "text": "Dashboard Configuration", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features/010-dashboard-configuration.md"}, {"file": "README.md", "link": "../setup/", "text": "Setup Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../setup"}, {"file": "README.md", "link": "../features/", "text": "Features Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features"}, {"file": "README.md", "link": "../testing/", "text": "Testing Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../testing"}, {"file": "README.md", "link": "../deployment/", "text": "Deployment Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../deployment"}, {"file": "130-table-features.md", "link": "#sorting-and-grouping", "text": "Sorting and Grouping", "status": "Anchor not found: #sorting-and-grouping"}, {"file": "130-table-features.md", "link": "../performance/query-optimization.md", "text": "Performance Optimization", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../performance/query-optimization.md"}, {"file": "130-table-features.md", "link": "../ui/table-components.md", "text": "UI Components", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../ui/table-components.md"}, {"file": "090-invoice-lines-resource.md", "link": "../business/pricing-models.md", "text": "Pricing Strategies", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../business/pricing-models.md"}, {"file": "090-invoice-lines-resource.md", "link": "../analytics/sales-reporting.md", "text": "Sales Analytics", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../analytics/sales-reporting.md"}, {"file": "100-employees-resource.md", "link": "../security/role-management.md", "text": "Role Management", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../security/role-management.md"}, {"file": "100-employees-resource.md", "link": "../analytics/hr-metrics.md", "text": "HR Analytics", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../analytics/hr-metrics.md"}, {"file": "110-users-resource.md", "link": "../security/authentication.md", "text": "Authentication Systems", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../security/authentication.md"}, {"file": "110-users-resource.md", "link": "../analytics/user-metrics.md", "text": "User Analytics", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../analytics/user-metrics.md"}, {"file": "000-index.md", "link": "#customer-management", "text": "Customer Management", "status": "Anchor not found: #customer-management"}, {"file": "000-index.md", "link": "#sales--invoicing", "text": "Sales & Invoicing", "status": "Anchor not found: #sales--invoicing"}, {"file": "000-index.md", "link": "../../010-chinook-models-guide.md", "text": "Chinook Models Guide", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../../010-chinook-models-guide.md"}, {"file": "000-index.md", "link": "../setup/000-index.md", "text": "Filament Setup Guide", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../setup/000-index.md"}, {"file": "000-index.md", "link": "../features/000-index.md", "text": "Filament Features", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../features/000-index.md"}, {"file": "000-index.md", "link": "../testing/000-index.md", "text": "Testing Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../testing/000-index.md"}, {"file": "000-index.md", "link": "../README.md", "text": "Filament Documentation Index", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../README.md"}, {"file": "060-media-types-resource.md", "link": "../testing/010-testing-strategy.md", "text": "Testing Strategy", "status": "Path outside base directory: .ai/guides/chinook/filament/resources/../testing/010-testing-strategy.md"}], "resolved_issues": [], "status": "PASS", "execution_time": 0.13834786415100098}]